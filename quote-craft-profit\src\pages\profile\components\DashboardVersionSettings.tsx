import React from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  LayoutDashboard,
  Zap,
  ExternalLink,
  Sparkles,
  Clock,
  Users,
} from "lucide-react";
import { useUserPreferences } from "@/hooks/useUserPreferences";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";

export const DashboardVersionSettings: React.FC = () => {
  const { preferences, updateDashboardVersion, isUpdating } =
    useUserPreferences();
  const navigate = useNavigate();

  const currentVersion = preferences?.dashboardVersion || "v1";

  const handleVersionChange = (version: "v1" | "v2") => {
    updateDashboardVersion(version);
    toast.success(`Dashboard version updated to ${version.toUpperCase()}`, {
      description: `You can now access the ${
        version === "v1" ? "classic" : "new wizard-based"
      } dashboard.`,
    });
  };

  const handlePreview = (version: "v1" | "v2") => {
    const route = version === "v1" ? "/" : "/dashboard-v2";
    navigate(route);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <LayoutDashboard className="h-5 w-5" />
          Dashboard Version
        </CardTitle>
        <CardDescription>
          Choose between the classic dashboard and the new wizard-based
          dashboard experience. Your preference will be applied when accessing
          the dashboard from navigation or direct links.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <RadioGroup
          value={currentVersion}
          onValueChange={handleVersionChange}
          disabled={isUpdating}
          className="space-y-4"
        >
          {/* Dashboard V1 Option */}
          <div className="flex items-start space-x-3 p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
            <RadioGroupItem value="v1" id="v1" className="mt-1" />
            <div className="flex-1 space-y-2">
              <div className="flex items-center gap-2">
                <Label
                  htmlFor="v1"
                  className="text-base font-medium cursor-pointer"
                >
                  Dashboard V1 (Classic)
                </Label>
                <Badge variant="secondary">Current</Badge>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                The traditional dashboard with overview cards, recent
                calculations, and quick stats.
              </p>
              <div className="flex items-center gap-4 text-xs text-gray-500">
                <div className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  Familiar interface
                </div>
                <div className="flex items-center gap-1">
                  <LayoutDashboard className="h-3 w-3" />
                  Overview focused
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePreview("v1")}
                className="mt-2"
              >
                <ExternalLink className="h-3 w-3 mr-1" />
                Preview V1
              </Button>
            </div>
          </div>

          {/* Dashboard V2 Option */}
          <div className="flex items-start space-x-3 p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
            <RadioGroupItem value="v2" id="v2" className="mt-1" />
            <div className="flex-1 space-y-2">
              <div className="flex items-center gap-2">
                <Label
                  htmlFor="v2"
                  className="text-base font-medium cursor-pointer"
                >
                  Dashboard V2 (Wizard)
                </Label>
                <Badge
                  variant="default"
                  className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                >
                  <Sparkles className="h-3 w-3 mr-1" />
                  New
                </Badge>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Guided event setup wizard that helps you find the perfect
                template based on your event requirements.
              </p>
              <div className="flex items-center gap-4 text-xs text-gray-500">
                <div className="flex items-center gap-1">
                  <Zap className="h-3 w-3" />
                  Quick setup
                </div>
                <div className="flex items-center gap-1">
                  <Users className="h-3 w-3" />
                  Template discovery
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePreview("v2")}
                className="mt-2"
              >
                <ExternalLink className="h-3 w-3 mr-1" />
                Preview V2
              </Button>
            </div>
          </div>
        </RadioGroup>

        {/* Feature Comparison */}
        <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
            What's different in V2?
          </h4>
          <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
            <li>• Step-by-step event setup wizard</li>
            <li>• Smart template recommendations based on your requirements</li>
            <li>• Event type, attendee count, and venue selection</li>
            <li>• Streamlined workflow for faster event planning</li>
          </ul>
        </div>

        {/* Current Selection Info */}
        <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <div className="flex items-center gap-2">
            <LayoutDashboard className="h-4 w-4 text-gray-600 dark:text-gray-400" />
            <span className="text-sm font-medium">
              Currently using: Dashboard {currentVersion.toUpperCase()}
            </span>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate("/dashboard")}
          >
            Go to Dashboard
            <ExternalLink className="h-3 w-3 ml-1" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
