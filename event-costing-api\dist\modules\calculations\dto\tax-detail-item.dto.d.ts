export declare class TaxDetailItemDto {
    id?: string;
    name: string;
    rate: number;
    amount?: number;
    applied_to_subtotal?: number;
    type?: string;
    basis?: string;
}
export declare class CreateTaxDto {
    tax_name: string;
    tax_rate: number;
    type?: string;
    basis?: string;
}
export declare class UpdateTaxDto {
    tax_name?: string;
    tax_rate?: number;
    type?: string;
    basis?: string;
}
