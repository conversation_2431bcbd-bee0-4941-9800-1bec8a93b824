"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var CalculationCompleteDataService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CalculationCompleteDataService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../../core/supabase/supabase.service");
const calculation_crud_service_1 = require("./calculation-crud.service");
const calculation_items_service_1 = require("../../calculation-items/calculation-items.service");
const packages_service_1 = require("../../packages/packages.service");
const categories_service_1 = require("../../categories/categories.service");
let CalculationCompleteDataService = CalculationCompleteDataService_1 = class CalculationCompleteDataService {
    supabaseService;
    calculationCrudService;
    calculationItemsService;
    packagesService;
    categoriesService;
    logger = new common_1.Logger(CalculationCompleteDataService_1.name);
    constructor(supabaseService, calculationCrudService, calculationItemsService, packagesService, categoriesService) {
        this.supabaseService = supabaseService;
        this.calculationCrudService = calculationCrudService;
        this.calculationItemsService = calculationItemsService;
        this.packagesService = packagesService;
        this.categoriesService = categoriesService;
    }
    async getCompleteData(calculationId, user) {
        this.logger.log(`Fetching complete calculation data for ID: ${calculationId}, User: ${user.email}`);
        const startTime = Date.now();
        try {
            const [calculationResult, lineItemsResult, packagesResult, categoriesResult,] = await Promise.allSettled([
                this.getCalculationDetails(calculationId, user),
                this.getLineItems(calculationId, user),
                this.getPackagesByCategory(calculationId, user),
                this.getCategories(),
            ]);
            const calculation = this.extractResult(calculationResult, 'calculation');
            const lineItems = this.extractResult(lineItemsResult, 'lineItems', []);
            const packages = this.extractResult(packagesResult, 'packages', []);
            const categories = this.extractResult(categoriesResult, 'categories', []);
            const loadTime = Date.now() - startTime;
            const errors = this.collectErrors([
                calculationResult,
                lineItemsResult,
                packagesResult,
                categoriesResult,
            ]);
            const result = {
                calculation,
                lineItems,
                packages,
                categories,
                metadata: {
                    loadTime,
                    cacheVersion: '1.0',
                    userId: user.id,
                    errors,
                    timestamp: new Date().toISOString(),
                },
            };
            this.logger.log(`Successfully fetched complete calculation data for ID: ${calculationId} in ${loadTime}ms`);
            return result;
        }
        catch (error) {
            this.logger.error(`Failed to fetch complete calculation data for ID: ${calculationId}`, error.stack);
            throw new common_1.InternalServerErrorException('Failed to load calculation data. Please try again.');
        }
    }
    async getCalculationDetails(calculationId, user) {
        const rawCalculation = await this.calculationCrudService.findCalculationRawById(calculationId, user);
        const calculation = {
            id: rawCalculation.id,
            name: rawCalculation.name,
            status: rawCalculation.status,
            event_type_id: rawCalculation.event_type_id,
            attendees: rawCalculation.attendees,
            event_start_date: rawCalculation.event_start_date,
            event_end_date: rawCalculation.event_end_date,
            notes: rawCalculation.notes,
            version_notes: rawCalculation.version_notes,
            created_at: rawCalculation.created_at,
            updated_at: rawCalculation.updated_at,
            created_by: rawCalculation.created_by,
            currency: rawCalculation.currency
                ? {
                    id: rawCalculation.currency.id,
                    code: rawCalculation.currency.code,
                }
                : null,
            city: rawCalculation.cities
                ? {
                    id: rawCalculation.cities.id,
                    name: rawCalculation.cities.name,
                }
                : null,
            client: rawCalculation.clients
                ? {
                    id: rawCalculation.clients.id,
                    client_name: rawCalculation.clients.client_name,
                    contact_person: rawCalculation.clients.contact_person,
                    email: rawCalculation.clients.email,
                }
                : null,
            event: rawCalculation.events
                ? {
                    id: rawCalculation.events.id,
                    event_name: rawCalculation.events.event_name,
                }
                : null,
            venues: [],
            line_items: [],
            custom_items: [],
            subtotal: rawCalculation.subtotal,
            taxes: this.transformTaxes(rawCalculation.calculation_taxes),
            discount: this.transformDiscount(rawCalculation.calculation_discounts, rawCalculation.discount),
            total: rawCalculation.total,
            total_cost: rawCalculation.total_cost,
            estimated_profit: rawCalculation.estimated_profit,
        };
        return calculation;
    }
    async getLineItems(calculationId, user) {
        return this.calculationItemsService.getCalculationItems(calculationId);
    }
    async getPackagesByCategory(calculationId, user) {
        const calculation = await this.calculationCrudService.findCalculationRawById(calculationId, user);
        const packagesResponse = await this.packagesService.getPackagesByCategory(calculation.currency_id, calculation.city_id || undefined, undefined, true);
        return packagesResponse.categories;
    }
    async getCategories() {
        return this.categoriesService.findAll();
    }
    extractResult(result, name, defaultValue) {
        if (result.status === 'fulfilled') {
            return result.value;
        }
        this.logger.warn(`Failed to fetch ${name}: ${result.reason?.message || 'Unknown error'}`);
        if (defaultValue !== undefined) {
            return defaultValue;
        }
        if (name === 'calculation') {
            throw result.reason;
        }
        return [];
    }
    collectErrors(results) {
        return results
            .filter(result => result.status === 'rejected')
            .map(result => result.reason?.message || 'Unknown error');
    }
    transformTaxes(calculationTaxes) {
        if (!calculationTaxes || !Array.isArray(calculationTaxes)) {
            return [];
        }
        return calculationTaxes.map(tax => ({
            id: tax.id,
            name: tax.tax_name,
            rate: parseFloat(tax.tax_rate.toString()) || 0,
            amount: parseFloat(tax.tax_amount.toString()) || 0,
            applied_to_subtotal: parseFloat(tax.applied_to_subtotal.toString()) || 0,
            type: tax.type || 'percentage',
            basis: tax.basis || 'subtotal',
        }));
    }
    transformDiscount(calculationDiscounts, legacyDiscount) {
        if (calculationDiscounts &&
            Array.isArray(calculationDiscounts) &&
            calculationDiscounts.length > 0) {
            const discount = calculationDiscounts[0];
            return {
                id: discount.id,
                name: discount.discount_name,
                type: discount.discount_type,
                value: parseFloat(discount.discount_value.toString()) || 0,
                amount: parseFloat(discount.discount_amount.toString()) || 0,
                applied_to_subtotal: parseFloat(discount.applied_to_subtotal.toString()) || 0,
            };
        }
        if (legacyDiscount && typeof legacyDiscount === 'object') {
            return legacyDiscount;
        }
        return null;
    }
};
exports.CalculationCompleteDataService = CalculationCompleteDataService;
exports.CalculationCompleteDataService = CalculationCompleteDataService = CalculationCompleteDataService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService,
        calculation_crud_service_1.CalculationCrudService,
        calculation_items_service_1.CalculationItemsService,
        packages_service_1.PackagesService,
        categories_service_1.CategoriesService])
], CalculationCompleteDataService);
//# sourceMappingURL=calculation-complete-data.service.js.map