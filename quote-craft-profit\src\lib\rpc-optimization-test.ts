/**
 * RPC Optimization Test Utilities
 * 
 * This module provides utilities for testing the optimized RPC functions
 * and comparing their performance against the original functions.
 */

import { supabase } from "@/integrations/supabase/client";
import { rpcOptimization, rpcDevUtils } from "./rpc-optimization";

interface TestResult {
  functionName: string;
  version: 'v1' | 'v2';
  success: boolean;
  executionTime: number;
  error?: string;
  result?: any;
}

class RpcOptimizationTester {
  private testResults: TestResult[] = [];

  /**
   * Test the recalculation functions (v1 vs v2)
   */
  async testRecalculationFunctions(calculationId: string): Promise<{
    v1Result: TestResult;
    v2Result: TestResult;
    comparison: { speedImprovement: number; bothSuccessful: boolean };
  }> {
    console.log(`[RPC Test] Testing recalculation functions for calculation ${calculationId}`);

    // Test v1 function
    const v1Result = await this.testFunction(
      'recalculate_calculation_totals',
      'v1',
      async () => {
        const { error } = await supabase.rpc("recalculate_calculation_totals", {
          p_calculation_id: calculationId,
        });
        if (error) throw error;
        return 'success';
      }
    );

    // Test v2 function
    const v2Result = await this.testFunction(
      'recalculate_calculation_totals',
      'v2',
      async () => {
        const { error } = await supabase.rpc("recalculate_calculation_totals_v2", {
          p_calculation_id: calculationId,
        });
        if (error) throw error;
        return 'success';
      }
    );

    const speedImprovement = v1Result.success && v2Result.success
      ? ((v1Result.executionTime - v2Result.executionTime) / v1Result.executionTime) * 100
      : 0;

    return {
      v1Result,
      v2Result,
      comparison: {
        speedImprovement,
        bothSuccessful: v1Result.success && v2Result.success,
      },
    };
  }

  /**
   * Test adding a package item (v1 vs v2)
   */
  async testAddPackageItem(
    calculationId: string,
    packageId: string,
    optionIds: string[] = []
  ): Promise<{
    v1Result: TestResult;
    v2Result: TestResult;
    comparison: { speedImprovement: number; bothSuccessful: boolean };
  }> {
    console.log(`[RPC Test] Testing add package item functions for calculation ${calculationId}`);

    // Get user ID and currency ID
    const { data: user } = await supabase.auth.getUser();
    const { data: calc } = await supabase
      .from("calculation_history")
      .select("currency_id")
      .eq("id", calculationId)
      .single();

    if (!user.user?.id || !calc?.currency_id) {
      throw new Error("User ID or currency ID not found");
    }

    const testParams = {
      p_calculation_id: calculationId,
      p_user_id: user.user.id,
      p_package_id: packageId,
      p_option_ids: optionIds,
      p_currency_id: calc.currency_id,
      p_quantity_override: 1,
      p_duration_override: 1,
      p_notes: "Test item",
    };

    // Test v1 function
    const v1Result = await this.testFunction(
      'add_package_item_and_recalculate',
      'v1',
      async () => {
        const { data, error } = await supabase.rpc("add_package_item_and_recalculate", testParams);
        if (error) throw error;
        return data;
      }
    );

    // Clean up v1 item if successful
    if (v1Result.success && v1Result.result) {
      await supabase
        .from("calculation_line_items")
        .delete()
        .eq("id", v1Result.result);
    }

    // Test v2 function
    const v2Result = await this.testFunction(
      'add_package_item_v2',
      'v2',
      async () => {
        const { data, error } = await supabase.rpc("add_package_item_v2", testParams);
        if (error) throw error;
        return data;
      }
    );

    // Clean up v2 item if successful
    if (v2Result.success && v2Result.result) {
      await supabase
        .from("calculation_line_items")
        .delete()
        .eq("id", v2Result.result);
    }

    const speedImprovement = v1Result.success && v2Result.success
      ? ((v1Result.executionTime - v2Result.executionTime) / v1Result.executionTime) * 100
      : 0;

    return {
      v1Result,
      v2Result,
      comparison: {
        speedImprovement,
        bothSuccessful: v1Result.success && v2Result.success,
      },
    };
  }

  /**
   * Test delete line item functions (v1 vs v2)
   */
  async testDeleteLineItem(
    calculationId: string,
    packageId: string
  ): Promise<{
    v1Result: TestResult;
    v2Result: TestResult;
    comparison: { speedImprovement: number; bothSuccessful: boolean };
  }> {
    console.log(`[RPC Test] Testing delete line item functions for calculation ${calculationId}`);

    // Get user ID and currency ID
    const { data: user } = await supabase.auth.getUser();
    const { data: calc } = await supabase
      .from("calculation_history")
      .select("currency_id")
      .eq("id", calculationId)
      .single();

    if (!user.user?.id || !calc?.currency_id) {
      throw new Error("User ID or currency ID not found");
    }

    // Create test items for deletion
    const testParams = {
      p_calculation_id: calculationId,
      p_user_id: user.user.id,
      p_package_id: packageId,
      p_option_ids: [],
      p_currency_id: calc.currency_id,
      p_quantity_override: 1,
      p_duration_override: 1,
      p_notes: "Test item for deletion",
    };

    // Create item for v1 test
    const { data: v1ItemId } = await supabase.rpc("add_package_item_v2", testParams);
    
    // Create item for v2 test
    const { data: v2ItemId } = await supabase.rpc("add_package_item_v2", testParams);

    // Test v1 delete (manual deletion + recalculation)
    const v1Result = await this.testFunction(
      'delete_line_item_manual',
      'v1',
      async () => {
        const { error } = await supabase
          .from("calculation_line_items")
          .delete()
          .eq("id", v1ItemId);
        if (error) throw error;

        const { error: recalcError } = await supabase.rpc("recalculate_calculation_totals", {
          p_calculation_id: calculationId,
        });
        if (recalcError) throw recalcError;

        return 'success';
      }
    );

    // Test v2 delete (unified RPC function)
    const v2Result = await this.testFunction(
      'delete_line_item_v2',
      'v2',
      async () => {
        const { error } = await supabase.rpc("delete_line_item_v2", {
          p_calculation_id: calculationId,
          p_item_id: v2ItemId,
          p_user_id: user.user.id,
          p_item_type: 'package',
        });
        if (error) throw error;
        return 'success';
      }
    );

    const speedImprovement = v1Result.success && v2Result.success
      ? ((v1Result.executionTime - v2Result.executionTime) / v1Result.executionTime) * 100
      : 0;

    return {
      v1Result,
      v2Result,
      comparison: {
        speedImprovement,
        bothSuccessful: v1Result.success && v2Result.success,
      },
    };
  }

  /**
   * Run comprehensive performance tests
   */
  async runComprehensiveTests(calculationId: string, packageId: string): Promise<{
    recalculationTest: any;
    addItemTest: any;
    deleteItemTest: any;
    overallResults: {
      averageSpeedImprovement: number;
      allTestsSuccessful: boolean;
      totalTests: number;
      successfulTests: number;
    };
  }> {
    console.log(`[RPC Test] Running comprehensive tests for calculation ${calculationId}`);

    const recalculationTest = await this.testRecalculationFunctions(calculationId);
    const addItemTest = await this.testAddPackageItem(calculationId, packageId);
    const deleteItemTest = await this.testDeleteLineItem(calculationId, packageId);

    const allResults = [
      recalculationTest.comparison,
      addItemTest.comparison,
      deleteItemTest.comparison,
    ];

    const successfulTests = allResults.filter(r => r.bothSuccessful).length;
    const averageSpeedImprovement = allResults
      .filter(r => r.bothSuccessful)
      .reduce((sum, r) => sum + r.speedImprovement, 0) / successfulTests || 0;

    return {
      recalculationTest,
      addItemTest,
      deleteItemTest,
      overallResults: {
        averageSpeedImprovement,
        allTestsSuccessful: successfulTests === allResults.length,
        totalTests: allResults.length,
        successfulTests,
      },
    };
  }

  /**
   * Helper function to test a single function with performance monitoring
   */
  private async testFunction(
    functionName: string,
    version: 'v1' | 'v2',
    testFn: () => Promise<any>
  ): Promise<TestResult> {
    const startTime = performance.now();
    let success = true;
    let error: string | undefined;
    let result: any;

    try {
      result = await testFn();
    } catch (err) {
      success = false;
      error = err instanceof Error ? err.message : 'Unknown error';
    }

    const endTime = performance.now();
    const executionTime = endTime - startTime;

    const testResult: TestResult = {
      functionName,
      version,
      success,
      executionTime,
      error,
      result,
    };

    this.testResults.push(testResult);

    // Also record in the main performance monitoring system
    rpcOptimization.recordPerformance({
      functionName,
      version,
      executionTime,
      success,
      error,
    });

    return testResult;
  }

  /**
   * Get all test results
   */
  getTestResults(): TestResult[] {
    return [...this.testResults];
  }

  /**
   * Clear test results
   */
  clearTestResults(): void {
    this.testResults = [];
  }

  /**
   * Log test results to console
   */
  logTestResults(): void {
    console.group('[RPC Test] Test Results Summary');
    this.testResults.forEach(result => {
      console.log(`${result.functionName} (${result.version}):`, {
        success: result.success,
        time: `${result.executionTime.toFixed(2)}ms`,
        error: result.error,
      });
    });
    console.groupEnd();
  }
}

// Export singleton instance
export const rpcTester = new RpcOptimizationTester();

// Export development utilities
export const rpcTestUtils = {
  /**
   * Quick test of all RPC functions
   */
  async quickTest(calculationId: string, packageId: string) {
    console.log('[RPC Test] Starting quick test...');
    
    const results = await rpcTester.runComprehensiveTests(calculationId, packageId);
    
    console.log('[RPC Test] Quick test completed:', results.overallResults);
    rpcTester.logTestResults();
    rpcDevUtils.logPerformanceStats();
    
    return results;
  },

  /**
   * Reset all test data
   */
  resetTestData() {
    rpcTester.clearTestResults();
    rpcDevUtils.resetPerformanceData();
    console.log('[RPC Test] Test data reset');
  },
};

export { RpcOptimizationTester };
export type { TestResult };
