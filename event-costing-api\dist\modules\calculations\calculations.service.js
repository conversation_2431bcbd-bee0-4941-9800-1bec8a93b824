"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var CalculationsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CalculationsService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../core/supabase/supabase.service");
const calculation_crud_service_1 = require("./services/calculation-crud.service");
const calculation_venue_service_1 = require("./services/calculation-venue.service");
const calculation_validation_service_1 = require("./services/calculation-validation.service");
const calculation_transformation_service_1 = require("./services/calculation-transformation.service");
const calculation_status_service_1 = require("./services/calculation-status.service");
const calculation_logic_service_1 = require("./calculation-logic.service");
const calculation_template_service_1 = require("./calculation-template.service");
let CalculationsService = CalculationsService_1 = class CalculationsService {
    supabaseService;
    calculationLogicService;
    calculationTemplateService;
    calculationCrudService;
    calculationVenueService;
    calculationValidationService;
    calculationTransformationService;
    calculationStatusService;
    logger = new common_1.Logger(CalculationsService_1.name);
    constructor(supabaseService, calculationLogicService, calculationTemplateService, calculationCrudService, calculationVenueService, calculationValidationService, calculationTransformationService, calculationStatusService) {
        this.supabaseService = supabaseService;
        this.calculationLogicService = calculationLogicService;
        this.calculationTemplateService = calculationTemplateService;
        this.calculationCrudService = calculationCrudService;
        this.calculationVenueService = calculationVenueService;
        this.calculationValidationService = calculationValidationService;
        this.calculationTransformationService = calculationTransformationService;
        this.calculationStatusService = calculationStatusService;
    }
    async createCalculation(createCalculationDto, user) {
        const calculationId = await this.calculationCrudService.createCalculation(createCalculationDto, user);
        if (createCalculationDto.venue_ids &&
            createCalculationDto.venue_ids.length > 0) {
            await this.calculationVenueService.addVenuesToCalculation(calculationId, createCalculationDto.venue_ids);
        }
        return calculationId;
    }
    async findUserCalculations(user, queryParams) {
        return this.calculationCrudService.findUserCalculations(user, queryParams);
    }
    async findCalculationById(id, user) {
        const raw = await this.calculationCrudService.findCalculationRawById(id, user);
        const venues = await this.calculationVenueService.fetchCalculationVenues(id);
        return this.calculationTransformationService.mapRawToDetailDto(raw, venues);
    }
    async updateCalculation(id, updateDto, user) {
        this.logger.log(`User ${user.id} updating calculation ${id}`);
        await this.calculationValidationService.checkCalculationOwnership(id, user.id);
        const { venue_ids, ...calculationUpdateData } = updateDto;
        await this.calculationCrudService.updateCalculationData(id, calculationUpdateData, user);
        if (venue_ids !== undefined) {
            await this.calculationVenueService.updateCalculationVenues(id, venue_ids || []);
        }
        await this.calculationCrudService.triggerRecalculation(id);
        this.logger.log(`Calculation ${id} updated, re-fetching details.`);
        return this.findCalculationById(id, user);
    }
    async deleteCalculation(id, user) {
        await this.calculationCrudService.deleteCalculation(id, user);
    }
    async checkCalculationOwnership(calculationId, userId) {
        await this.calculationValidationService.checkCalculationOwnership(calculationId, userId);
    }
    async findCalculationForExport(calculationId, userId) {
        this.logger.log(`[EXPORT] Starting findCalculationForExport for calc ${calculationId}, user ${userId}`);
        try {
            this.logger.log(`[EXPORT] Checking ownership for calc ${calculationId}, user ${userId}`);
            await this.checkCalculationOwnership(calculationId, userId);
            this.logger.log(`[EXPORT] Ownership check passed for calc ${calculationId}`);
            const systemUser = {
                id: userId,
                email: '<EMAIL>',
                app_metadata: {},
                user_metadata: {},
                aud: '',
                created_at: '',
            };
            this.logger.log(`[EXPORT] Fetching raw calculation data for calc ${calculationId}`);
            const raw = await this.calculationCrudService.findCalculationRawById(calculationId, systemUser);
            this.logger.log(`[EXPORT] Raw calculation data fetched successfully for calc ${calculationId}`);
            this.logger.log(`[EXPORT] Fetching venues for calc ${calculationId}`);
            const venues = await this.calculationVenueService.fetchCalculationVenues(calculationId);
            this.logger.log(`[EXPORT] Venues fetched successfully for calc ${calculationId}, count: ${venues?.length || 0}`);
            this.logger.log(`[EXPORT] Transforming data for calc ${calculationId}`);
            const result = this.calculationTransformationService.mapRawToDetailDto(raw, venues);
            this.logger.log(`[EXPORT] Data transformation completed successfully for calc ${calculationId}`);
            return result;
        }
        catch (error) {
            this.logger.error(`[EXPORT] Error in findCalculationForExport for calc ${calculationId}, user ${userId}: ${error instanceof Error ? error.message : String(error)}`, error instanceof Error ? error.stack : undefined);
            throw error;
        }
    }
    async updateStatus(id, updateStatusDto, userId) {
        await this.calculationStatusService.updateStatus(id, updateStatusDto, userId);
    }
    async getCalculationSummary(id, user) {
        this.logger.log(`Fetching calculation summary for ID: ${id}`);
        await this.checkCalculationOwnership(id, user.id);
        const supabase = this.supabaseService.getClient();
        const { data: calculation, error: calcError } = await supabase
            .from('calculation_history')
            .select(`
        id, name, total, attendees, event_type_id,
        currency:currencies(id, code),
        city:cities(id, name),
        event_type:event_types(id, name),
        venues:calculation_venues(
          venue:venues(id, name)
        )
      `)
            .eq('id', id)
            .eq('is_deleted', false)
            .single();
        if (calcError) {
            this.logger.error(`Error fetching calculation ${id}: ${calcError.message}`);
            throw new common_1.InternalServerErrorException('Could not retrieve calculation.');
        }
        if (!calculation) {
            throw new common_1.NotFoundException(`Calculation with ID ${id} not found.`);
        }
        const { count: standardPackagesCount, error: packagesError } = await supabase
            .from('calculation_line_items')
            .select('*', { count: 'exact', head: true })
            .eq('calculation_id', id)
            .not('package_id', 'is', null);
        if (packagesError) {
            this.logger.error(`Error counting standard packages: ${packagesError.message}`);
            throw new common_1.InternalServerErrorException('Could not count standard packages.');
        }
        const { count: customItemsCount, error: customError } = await supabase
            .from('calculation_custom_items')
            .select('*', { count: 'exact', head: true })
            .eq('calculation_id', id);
        if (customError) {
            this.logger.error(`Error counting custom items: ${customError.message}`);
            throw new common_1.InternalServerErrorException('Could not count custom items.');
        }
        const transformedCalculation = this.calculationTransformationService.mapCalculationSummary(calculation);
        const summary = {
            ...transformedCalculation,
            standardPackagesCount: standardPackagesCount || 0,
            customItemsCount: customItemsCount || 0,
        };
        this.logger.log(`Calculation summary for ${id}: ${JSON.stringify({
            ...summary,
            venues: `[${summary.venues?.length || 0} venues]`,
        })}`);
        return summary;
    }
    async addTax(calculationId, createTaxDto) {
        this.logger.log(`Adding tax to calculation ${calculationId}`);
        const supabase = this.supabaseService.getClient();
        const { data: calculation, error: calcError } = await supabase
            .from('calculation_history')
            .select('subtotal')
            .eq('id', calculationId)
            .single();
        if (calcError || !calculation) {
            throw new common_1.NotFoundException(`Calculation ${calculationId} not found`);
        }
        const subtotal = parseFloat(calculation.subtotal) || 0;
        const taxAmount = (subtotal * createTaxDto.tax_rate) / 100;
        const { data: newTax, error: taxError } = await supabase
            .from('calculation_taxes')
            .insert({
            calculation_id: calculationId,
            tax_name: createTaxDto.tax_name,
            tax_rate: createTaxDto.tax_rate,
            tax_amount: taxAmount,
            applied_to_subtotal: subtotal,
        })
            .select()
            .single();
        if (taxError) {
            this.logger.error(`Failed to add tax: ${taxError.message}`);
            throw new common_1.InternalServerErrorException('Failed to add tax');
        }
        await this.recalculateCalculationTotals(calculationId);
        return newTax;
    }
    async getTaxes(calculationId) {
        this.logger.log(`Getting taxes for calculation ${calculationId}`);
        const supabase = this.supabaseService.getClient();
        const { data: taxes, error } = await supabase
            .from('calculation_taxes')
            .select('*')
            .eq('calculation_id', calculationId)
            .order('created_at', { ascending: true });
        if (error) {
            this.logger.error(`Failed to get taxes: ${error.message}`);
            throw new common_1.InternalServerErrorException('Failed to get taxes');
        }
        return taxes || [];
    }
    async updateTax(calculationId, taxId, updateTaxDto) {
        this.logger.log(`Updating tax ${taxId} for calculation ${calculationId}`);
        const supabase = this.supabaseService.getClient();
        const { data: calculation, error: calcError } = await supabase
            .from('calculation_history')
            .select('subtotal')
            .eq('id', calculationId)
            .single();
        if (calcError || !calculation) {
            throw new common_1.NotFoundException(`Calculation ${calculationId} not found`);
        }
        const subtotal = parseFloat(calculation.subtotal) || 0;
        const updateData = { ...updateTaxDto };
        if (updateTaxDto.tax_rate !== undefined) {
            updateData.tax_amount = (subtotal * updateTaxDto.tax_rate) / 100;
            updateData.applied_to_subtotal = subtotal;
        }
        const { data: updatedTax, error: taxError } = await supabase
            .from('calculation_taxes')
            .update(updateData)
            .eq('id', taxId)
            .eq('calculation_id', calculationId)
            .select()
            .single();
        if (taxError) {
            this.logger.error(`Failed to update tax: ${taxError.message}`);
            throw new common_1.InternalServerErrorException('Failed to update tax');
        }
        if (!updatedTax) {
            throw new common_1.NotFoundException(`Tax ${taxId} not found`);
        }
        await this.recalculateCalculationTotals(calculationId);
        return updatedTax;
    }
    async removeTax(calculationId, taxId) {
        this.logger.log(`Removing tax ${taxId} from calculation ${calculationId}`);
        const supabase = this.supabaseService.getClient();
        const { error } = await supabase
            .from('calculation_taxes')
            .delete()
            .eq('id', taxId)
            .eq('calculation_id', calculationId);
        if (error) {
            this.logger.error(`Failed to remove tax: ${error.message}`);
            throw new common_1.InternalServerErrorException('Failed to remove tax');
        }
        await this.recalculateCalculationTotals(calculationId);
    }
    async recalculateCalculationTotals(calculationId) {
        const supabase = this.supabaseService.getClient();
        const { error } = await supabase.rpc('recalculate_calculation_totals_v3', {
            calc_id: calculationId,
        });
        if (error) {
            this.logger.error(`Failed to recalculate totals: ${error.message}`);
            throw new common_1.InternalServerErrorException('Failed to recalculate totals');
        }
    }
};
exports.CalculationsService = CalculationsService;
exports.CalculationsService = CalculationsService = CalculationsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService,
        calculation_logic_service_1.CalculationLogicService,
        calculation_template_service_1.CalculationTemplateService,
        calculation_crud_service_1.CalculationCrudService,
        calculation_venue_service_1.CalculationVenueService,
        calculation_validation_service_1.CalculationValidationService,
        calculation_transformation_service_1.CalculationTransformationService,
        calculation_status_service_1.CalculationStatusService])
], CalculationsService);
//# sourceMappingURL=calculations.service.js.map