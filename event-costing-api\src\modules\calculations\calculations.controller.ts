import {
  Controller,
  Post,
  Body,
  Get,
  Put,
  Patch,
  Delete,
  Query,
  Param,
  ParseU<PERSON><PERSON><PERSON>e,
  <PERSON>Guards,
  <PERSON>gger,
  HttpStatus,
  HttpCode,
  NotFoundException,
} from '@nestjs/common';
import { CalculationsService } from './calculations.service';
import { CalculationTemplateService } from './calculation-template.service';
import { CalculationLogicService } from './calculation-logic.service';
import { CreateCalculationDto } from './dto/create-calculation.dto';
import { CreateCalculationFromTemplateDto } from './dto/create-calculation-from-template.dto';
import { ListCalculationsDto } from './dto/list-calculations.dto';
import { CalculationDetailDto } from './dto/calculation-detail.dto';
import { UpdateCalculationDto } from './dto/update-calculation.dto';
import { CreateTaxDto, UpdateTaxDto } from './dto/tax-detail-item.dto';
import { PaginatedResponseDto } from '../../shared/dtos/paginated-response.dto';
import { CalculationSummaryDto } from './dto/paginated-calculations.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { GetCurrentUser } from '../auth/decorators/get-current-user.decorator';
import { User } from '@supabase/supabase-js';
import {
  ApiTags,
  ApiBearerAuth,
  ApiParam,
  ApiOkResponse,
  ApiResponse,
  ApiOperation,
  ApiProperty,
  ApiNoContentResponse,
  ApiBadRequestResponse,
} from '@nestjs/swagger';
import { CalculationTotalsDto } from './dto/calculation-totals.dto';
import { UpdateCalculationStatusDto } from './dto/update-calculation-status.dto';
import { CalculationIdResponse } from './dto/calculation-id-response.dto';
import { CalculationCompleteDataDto } from './dto/calculation-complete-data.dto';
import { CalculationCompleteDataService } from './services/calculation-complete-data.service';

@ApiTags('Calculations')
@ApiBearerAuth()
@Controller('calculations')
@UseGuards(JwtAuthGuard)
export class CalculationsController {
  private readonly logger = new Logger(CalculationsController.name);

  constructor(
    private readonly calculationsService: CalculationsService,
    private readonly calculationTemplateService: CalculationTemplateService,
    private readonly calculationLogicService: CalculationLogicService,
    private readonly calculationCompleteDataService: CalculationCompleteDataService,
  ) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a new blank calculation' })
  @ApiOkResponse({
    description: 'Calculation created',
    type: CalculationIdResponse,
  })
  async createCalculation(
    @Body() createCalculationDto: CreateCalculationDto,
    @GetCurrentUser() user: User,
  ): Promise<CalculationIdResponse> {
    this.logger.log(
      `User ${user.email} creating calculation: ${JSON.stringify({
        ...createCalculationDto,
        venue_ids: createCalculationDto.venue_ids?.length
          ? `[${createCalculationDto.venue_ids.length} venues]`
          : 'none',
      })}`,
    );
    const calculationId = await this.calculationsService.createCalculation(
      createCalculationDto,
      user,
    );
    return { id: calculationId };
  }

  @Post('from-template/:templateId')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create a new calculation from a template with customization',
  })
  @ApiParam({ name: 'templateId', type: 'string', format: 'uuid' })
  @ApiOkResponse({
    description: 'Calculation created from template',
    type: CalculationIdResponse,
  })
  async createFromTemplate(
    @Param('templateId', ParseUUIDPipe) templateId: string,
    @Body() customizationDto: CreateCalculationFromTemplateDto,
    @GetCurrentUser() user: User,
  ): Promise<CalculationIdResponse> {
    this.logger.log(
      `User ${user.email} creating calculation from template ID: ${templateId} with customization: ${JSON.stringify(
        {
          ...customizationDto,
          venueIds: customizationDto.venueIds?.length
            ? `[${customizationDto.venueIds.length} venues]`
            : 'none',
        },
      )}`,
    );
    const calculationId =
      await this.calculationTemplateService.createFromTemplate(
        templateId,
        customizationDto,
        user,
      );
    return { id: calculationId };
  }

  @Get()
  @ApiOperation({ summary: 'List calculations for the current user' })
  @ApiOkResponse({ type: PaginatedResponseDto<CalculationSummaryDto> })
  async findUserCalculations(
    @Query() queryDto: ListCalculationsDto,
    @GetCurrentUser() user: User,
  ): Promise<PaginatedResponseDto<CalculationSummaryDto>> {
    this.logger.log(
      `User ${user.email} listing calculations with query: ${JSON.stringify(queryDto)}`,
    );
    return this.calculationsService.findUserCalculations(user, queryDto);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get calculation details by ID' })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  @ApiOkResponse({ type: CalculationDetailDto })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Calculation not found or access denied.',
  })
  async findCalculationById(
    @Param('id', ParseUUIDPipe) id: string,
    @GetCurrentUser() user: User,
  ): Promise<CalculationDetailDto> {
    this.logger.log(`User ${user.email} fetching calculation ID: ${id}`);
    return this.calculationsService.findCalculationById(id, user);
  }

  @Get(':id/complete-data')
  @ApiOperation({
    summary: 'Get complete calculation data in a single API call',
    description:
      'Consolidated endpoint that returns calculation details, line items, packages by category, and categories in a single response. Replaces the need for 4 separate API calls from the frontend.',
  })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  @ApiOkResponse({
    description: 'Complete calculation data with metadata',
    type: CalculationCompleteDataDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Calculation not found or access denied.',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Failed to load calculation data.',
  })
  async getCompleteCalculationData(
    @Param('id', ParseUUIDPipe) id: string,
    @GetCurrentUser() user: User,
  ): Promise<CalculationCompleteDataDto> {
    this.logger.log(
      `User ${user.email} fetching complete calculation data for ID: ${id}`,
    );
    return this.calculationCompleteDataService.getCompleteData(id, user);
  }

  @Get(':id/totals')
  @ApiOperation({ summary: 'Get calculated totals for a calculation' })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  @ApiOkResponse({ type: CalculationTotalsDto })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Calculation not found or access denied.',
  })
  async getTotals(
    @Param('id', ParseUUIDPipe) id: string,
    @GetCurrentUser() user: User,
  ): Promise<CalculationTotalsDto> {
    await this.calculationsService.checkCalculationOwnership(id, user.id);
    const totals =
      await this.calculationLogicService.fetchAndCalculateTotals(id);

    if (!totals) {
      this.logger.warn(
        `Could not fetch/calculate totals for calc ID: ${id} after ownership check`,
      );
      throw new NotFoundException(
        `Calculation totals could not be determined for ID: ${id}`,
      );
    }

    return totals;
  }

  @Get(':id/summary')
  @ApiOperation({ summary: 'Get calculation summary for template creation' })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  @ApiOkResponse({
    description: 'Calculation summary with package and custom item counts',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', format: 'uuid' },
        name: { type: 'string' },
        total: { type: 'number' },
        standardPackagesCount: { type: 'number' },
        customItemsCount: { type: 'number' },
        currency: {
          type: 'object',
          properties: { id: { type: 'string' }, code: { type: 'string' } },
        },
        attendees: { type: 'number', nullable: true },
        event_type: { type: 'string', nullable: true },
        city: {
          type: 'object',
          nullable: true,
          properties: { id: { type: 'string' }, name: { type: 'string' } },
        },
        venues: {
          type: 'array',
          items: {
            type: 'object',
            properties: { id: { type: 'string' }, name: { type: 'string' } },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Calculation not found or access denied.',
  })
  async getCalculationSummary(
    @Param('id', ParseUUIDPipe) id: string,
    @GetCurrentUser() user: User,
  ): Promise<any> {
    this.logger.log(
      `User ${user.email} fetching calculation summary for ID: ${id}`,
    );
    return this.calculationsService.getCalculationSummary(id, user);
  }

  @Put(':id')
  @ApiOperation({
    summary: 'Update calculation details (excluding status, taxes, discount)',
  })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  @ApiOkResponse({ type: CalculationDetailDto })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Calculation not found or access denied.',
  })
  async updateCalculation(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateCalculationDto: UpdateCalculationDto,
    @GetCurrentUser() user: User,
  ): Promise<CalculationDetailDto> {
    this.logger.log(
      `User ${user.email} updating calculation ID: ${id} with data: ${JSON.stringify(
        {
          ...updateCalculationDto,
          venue_ids: updateCalculationDto.venue_ids
            ? Array.isArray(updateCalculationDto.venue_ids)
              ? `[${updateCalculationDto.venue_ids.length} venues]`
              : updateCalculationDto.venue_ids
            : 'unchanged',
        },
      )}`,
    );
    return this.calculationsService.updateCalculation(
      id,
      updateCalculationDto,
      user,
    );
  }

  @Patch(':id/status')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Update the status of a calculation' })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  @ApiNoContentResponse({
    description: 'Calculation status updated successfully.',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Calculation not found or access denied.',
  })
  @ApiBadRequestResponse({
    description: 'Invalid status transition requested.',
  })
  async updateStatus(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateStatusDto: UpdateCalculationStatusDto,
    @GetCurrentUser() user: User,
  ): Promise<void> {
    this.logger.log(
      `User ${user.email} attempting update status for calc ${id} to ${updateStatusDto.status}`,
    );
    await this.calculationsService.updateStatus(id, updateStatusDto, user.id);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Soft delete a calculation' })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Calculation soft deleted successfully.',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Calculation not found or user does not have permission.',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'An error occurred during deletion.',
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(
    @Param('id', ParseUUIDPipe) id: string,
    @GetCurrentUser() user: User,
  ): Promise<void> {
    this.logger.log(`User ${user.email} deleting calculation ID: ${id}`);
    return this.calculationsService.deleteCalculation(id, user);
  }

  @Post(':id/recalculate')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Recalculate totals for a calculation' })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  @ApiNoContentResponse({
    description: 'Calculation totals recalculated successfully.',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Calculation not found or access denied.',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'An error occurred during recalculation.',
  })
  async recalculateTotals(
    @Param('id', ParseUUIDPipe) id: string,
    @GetCurrentUser() user: User,
  ): Promise<void> {
    this.logger.log(
      `User ${user.email} recalculating totals for calculation ID: ${id}`,
    );
    // First check if the user has access to this calculation
    await this.calculationsService.checkCalculationOwnership(id, user.id);
    // Then trigger the recalculation
    await this.calculationLogicService.recalculateTotals(id);
  }

  // Tax Management Endpoints

  @Post(':id/taxes')
  @ApiOperation({ summary: 'Add a tax to a calculation' })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  @ApiOkResponse({
    description: 'Tax added successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', format: 'uuid' },
        tax_name: { type: 'string' },
        tax_rate: { type: 'number' },
        tax_amount: { type: 'number' },
        applied_to_subtotal: { type: 'number' },
      },
    },
  })
  async addTax(
    @Param('id', ParseUUIDPipe) calculationId: string,
    @Body() createTaxDto: CreateTaxDto,
    @GetCurrentUser() user: User,
  ) {
    await this.calculationsService.checkCalculationOwnership(
      calculationId,
      user.id,
    );
    return this.calculationsService.addTax(calculationId, createTaxDto);
  }

  @Get(':id/taxes')
  @ApiOperation({ summary: 'Get all taxes for a calculation' })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  @ApiOkResponse({
    description: 'List of taxes for the calculation',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' },
          tax_name: { type: 'string' },
          tax_rate: { type: 'number' },
          tax_amount: { type: 'number' },
          applied_to_subtotal: { type: 'number' },
        },
      },
    },
  })
  async getTaxes(
    @Param('id', ParseUUIDPipe) calculationId: string,
    @GetCurrentUser() user: User,
  ) {
    await this.calculationsService.checkCalculationOwnership(
      calculationId,
      user.id,
    );
    return this.calculationsService.getTaxes(calculationId);
  }

  @Put(':id/taxes/:taxId')
  @ApiOperation({ summary: 'Update a specific tax' })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  @ApiParam({ name: 'taxId', type: 'string', format: 'uuid' })
  @ApiOkResponse({
    description: 'Tax updated successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', format: 'uuid' },
        tax_name: { type: 'string' },
        tax_rate: { type: 'number' },
        tax_amount: { type: 'number' },
        applied_to_subtotal: { type: 'number' },
      },
    },
  })
  async updateTax(
    @Param('id', ParseUUIDPipe) calculationId: string,
    @Param('taxId', ParseUUIDPipe) taxId: string,
    @Body() updateTaxDto: UpdateTaxDto,
    @GetCurrentUser() user: User,
  ) {
    await this.calculationsService.checkCalculationOwnership(
      calculationId,
      user.id,
    );
    return this.calculationsService.updateTax(
      calculationId,
      taxId,
      updateTaxDto,
    );
  }

  @Delete(':id/taxes/:taxId')
  @ApiOperation({ summary: 'Remove a specific tax' })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  @ApiParam({ name: 'taxId', type: 'string', format: 'uuid' })
  @ApiNoContentResponse({ description: 'Tax removed successfully' })
  @HttpCode(HttpStatus.NO_CONTENT)
  async removeTax(
    @Param('id', ParseUUIDPipe) calculationId: string,
    @Param('taxId', ParseUUIDPipe) taxId: string,
    @GetCurrentUser() user: User,
  ) {
    await this.calculationsService.checkCalculationOwnership(
      calculationId,
      user.id,
    );
    return this.calculationsService.removeTax(calculationId, taxId);
  }

  // NOTE: Routes for adding/deleting line items removed from this controller.
  // They should exist in CalculationItemsController.
}
