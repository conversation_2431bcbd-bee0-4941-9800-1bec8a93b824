# Calculation Detail API Architecture Analysis

## Executive Summary

This analysis examines the current API architecture and data flow patterns in the calculation detail functionality of the Quote Craft Profit application. The system currently implements a **hybrid architecture** that combines direct Supabase integration with backend API endpoints, with ongoing migration toward a consolidated API approach.

## 1. File Analysis - CalculationDetailPage.tsx

### Core Structure

- **Main Component**: `CalculationDetailPage.tsx` (168 lines)
- **Primary Hook**: `useCalculationDetailComplete` - consolidates all calculation detail state and actions
- **Architecture Pattern**: Performance-optimized with memoization and stable dependencies
- **Error Handling**: Comprehensive error boundaries and UUID validation

### Key Dependencies

```typescript
// Main hook chain
useCalculationDetailComplete()
  → useCalculationDetail()
    → useOptimizedCalculationDetail()
      → useOptimizedCalculationDetailCore()
        → useParallelCalculationData()
```

## 2. Service Layer Investigation

### Current Hybrid Architecture

#### A. Direct Supabase Integration (Frontend → Supabase)

**Location**: `src/services/calculations/calculationDataService.ts`

**Operations using Supabase client**:

- `getAllCalculations()` - Fetches user's calculations with filtering
- `getCalculationById()` - Retrieves calculation details with related data
- `getPackagesByCategory()` - Complex package filtering by city/venue
- `createCalculation()` - Creates new calculations with venue associations

**Characteristics**:

- Direct database queries with complex joins
- Row-level security (RLS) for user isolation
- Real-time data access
- Complex filtering logic (city, venue, currency)

#### B. Backend API Integration (Frontend → Backend API → Supabase)

**Location**: `src/services/calculations/calculationCompleteDataService.ts`

**Operations using API client**:

- `getCompleteCalculationData()` - Consolidated endpoint replacing 4 separate calls
- `getCalculationSummary()` - Template creation summaries
- Line item operations (add/update/delete)
- Export functionality

**Characteristics**:

- RESTful API with authentication
- Data transformation and business logic
- Consolidated endpoints for performance
- Structured error handling

### Migration Status - Consolidated Endpoint Pattern

The system implements an intelligent fallback mechanism:

```typescript
// Consolidated endpoint availability check
const { data: isConsolidatedAvailable } = useQuery({
  queryKey: ["consolidated-endpoint-availability"],
  queryFn: isConsolidatedEndpointAvailable,
});

// Automatic fallback to legacy endpoints
const shouldUseLegacy = !isConsolidatedAvailable || consolidatedQuery.isError;
```

**Performance Impact**:

- Consolidated: 1 API call vs Legacy: 4 parallel API calls
- Estimated 30-50% reduction in page load time
- Reduced network overhead and complexity

## 3. Hook Dependencies Analysis

### React Query Hooks and Caching Strategies

#### Primary Data Fetching

```typescript
// Core calculation data with parallel optimization
const coreData = useOptimizedCalculationDetailCore(id, {
  enabled: enabled && enableParallelLoading,
  staleTime: 5 * 60 * 1000, // 5 minutes
  gcTime: 10 * 60 * 1000, // 10 minutes
  retryDelay: 1000,
  maxRetries: 3,
});
```

#### Query Keys Structure

```typescript
QUERY_KEYS = {
  calculation: (id) => ["calculation", id],
  lineItems: (id) => ["lineItems", id],
  packagesByCategory: (id) => ["packagesByCategory", id],
  categories: ["categories"],
};
```

#### Error Handling Approaches

- **Optimistic Updates**: For line item modifications
- **Automatic Retries**: 3 attempts with exponential backoff
- **Cache Invalidation**: Strategic invalidation on mutations
- **Fallback Mechanisms**: Graceful degradation to legacy endpoints

### Custom Hooks Architecture

#### Financial Management

- `useTaxesAndDiscounts()` - State management for taxes/discounts
- `useFinancialCalculations()` - Real-time calculation updates
- `useCalculationActions()` - Status changes, deletion, navigation

#### UI State Management

- `useCalculationDetailUI()` - Form states, editing modes
- `usePackageForms()` - Package selection and options
- `useLineItemManagement()` - CRUD operations for line items

## 4. API Architecture Mapping

### Current Hybrid Operations Categorization

#### Direct Supabase Integration

| Operation              | Service                   | Reason                   |
| ---------------------- | ------------------------- | ------------------------ |
| User calculations list | `getAllCalculations()`    | User filtering, RLS      |
| Calculation details    | `getCalculationById()`    | Complex joins, real-time |
| Package filtering      | `getPackagesByCategory()` | City/venue logic         |
| Line item CRUD         | `lineItemService.ts`      | Real-time updates        |
| Tax/discount updates   | `useTaxesAndDiscounts()`  | Immediate persistence    |

#### Backend API Integration

| Operation             | Service                        | Reason                   |
| --------------------- | ------------------------------ | ------------------------ |
| Consolidated data     | `getCompleteCalculationData()` | Performance optimization |
| Calculation summaries | `getCalculationSummary()`      | Business logic           |
| Export operations     | Export services                | File generation          |
| Template operations   | Template services              | Complex transformations  |

### Data Transformation Patterns

#### Frontend → Supabase

```typescript
// Direct database schema mapping
const { data, error } = await supabase.from("calculation_history").select(`
    *,
    currency:currencies(code),
    city:cities(name),
    client:clients(client_name)
  `);
```

#### Frontend → Backend API → Supabase

```typescript
// Structured API responses with metadata
interface CalculationCompleteData {
  calculation: CalculationDetails;
  lineItems: LineItem[];
  packages: CategoryWithPackages[];
  categories: Category[];
  metadata: {
    loadTime: number;
    cacheVersion: string;
    errors?: string[];
  };
}
```

## 5. Data Flow Documentation

### Read Operations Flow

#### Calculation Detail Page Load

1. **UUID Validation** → `useSafeUUID()`
2. **Consolidated Data Check** → `isConsolidatedEndpointAvailable()`
3. **Primary Data Fetch** → `getCompleteCalculationData()` OR legacy parallel queries
4. **Data Transformation** → Frontend type mapping
5. **Cache Storage** → React Query cache with 5min stale time
6. **UI Rendering** → Memoized components with stable props

#### Line Items Management

1. **Real-time Fetch** → `getCalculationLineItems()` (Supabase)
2. **Package Data** → `getPackagesByCategory()` (Supabase)
3. **Options Loading** → `getBatchPackageOptions()` (API)
4. **Form State** → `usePackageForms()` local state

### Write Operations Flow

#### Line Item Addition

1. **Form Validation** → Zod schemas
2. **Optimistic Update** → Local state update
3. **Database Write** → `addLineItem()` (Supabase RPC)
4. **Recalculation** → `recalculateCalculationTotals()` (Supabase RPC)
5. **Cache Invalidation** → React Query invalidation
6. **UI Feedback** → Toast notifications

#### Status Changes

1. **Tax/Discount Save** → `saveTaxesAndDiscount()` (Supabase)
2. **Status Update** → `updateCalculation()` (Supabase)
3. **Navigation** → Conditional redirect based on status
4. **Cache Refresh** → Query invalidation

### Real-time Updates

- **No WebSocket/Realtime**: Currently using polling via React Query
- **Cache Strategy**: 5-minute stale time with background refetch
- **Optimistic Updates**: For immediate UI feedback
- **Conflict Resolution**: Last-write-wins with error rollback

## 6. Performance Optimizations

### Current Optimizations

- **Parallel Data Loading**: Consolidated endpoint reduces 4 calls to 1
- **Memoization**: Extensive use of `useMemo`, `useCallback`, `React.memo`
- **Stable Dependencies**: Object reference tracking and stabilization
- **Query Deduplication**: React Query automatic deduplication
- **Background Refetch**: Stale-while-revalidate pattern

### Performance Metrics

- **Page Load Time**: 30-50% improvement with consolidated endpoint
- **Re-render Reduction**: Stabilized dependencies prevent unnecessary renders
- **Memory Usage**: Optimized with proper garbage collection times
- **Network Requests**: Reduced from 4 to 1 for initial load

## 7. Migration Considerations

### Strengths of Current Architecture

- **Flexibility**: Can use either Supabase or API based on requirements
- **Performance**: Optimized data fetching with intelligent fallbacks
- **Reliability**: Graceful degradation when endpoints are unavailable
- **Developer Experience**: Clear separation of concerns

### Areas for Improvement

- **Consistency**: Mixed patterns can be confusing for new developers
- **Maintenance**: Dual code paths require more testing and maintenance
- **Type Safety**: Some type transformations could be more robust
- **Error Handling**: Could benefit from more standardized error patterns

### Recommended Next Steps

1. **Complete API Migration**: Move remaining Supabase operations to backend
2. **Standardize Error Handling**: Unified error response format
3. **Enhance Type Safety**: Stronger typing for API responses
4. **Performance Monitoring**: Add metrics for API vs Supabase performance
5. **Documentation**: Update API contracts and integration guides

## 8. Detailed Service Analysis

### Core Services Breakdown

#### calculationDataService.ts (617 lines)

**Purpose**: Primary data operations with direct Supabase integration
**Key Functions**:

- `getAllCalculations()` - User-filtered calculation list with RLS
- `getCalculationById()` - Complex joins for calculation details
- `getPackagesByCategory()` - Advanced filtering by city/venue/currency
- `createCalculation()` - Multi-table inserts with venue associations

**Architecture Pattern**: Direct Supabase client usage
**Performance**: Optimized queries with selective field loading
**Security**: Row-level security for user isolation

#### calculationCompleteDataService.ts (287 lines)

**Purpose**: Consolidated API endpoint for performance optimization
**Key Functions**:

- `getCompleteCalculationData()` - Single call replacing 4 endpoints
- `isConsolidatedEndpointAvailable()` - Feature detection
- `compareApiPerformance()` - Performance benchmarking

**Architecture Pattern**: Backend API with intelligent fallback
**Performance**: 30-50% load time improvement
**Reliability**: Graceful degradation to legacy endpoints

#### lineItemService.ts (716 lines)

**Purpose**: Unified line item operations (Supabase + API)
**Key Functions**:

- `getCalculationLineItems()` - Complex joins for package + custom items
- `addLineItem()` - RPC-based insertion with recalculation
- `removeLineItem()` - Soft deletion with total recalculation
- Dual implementation: Supabase RPC + Backend API

**Architecture Pattern**: Hybrid with preference for Supabase RPC
**Performance**: Optimistic updates with rollback capability
**Data Integrity**: Automatic recalculation triggers

### API Endpoints Structure

#### Consolidated Endpoints (New Architecture)

```typescript
CALCULATIONS: {
  GET_COMPLETE_DATA: (id) => `/calculations/${id}/complete-data`,
  // Replaces 4 separate calls:
  // - GET /calculations/:id
  // - GET /calculations/:id/items
  // - GET /calculations/:id/available-packages
  // - GET /categories
}
```

#### Legacy Endpoints (Fallback)

```typescript
CALCULATIONS: {
  GET_BY_ID: (id) => `/calculations/${id}`,
  LINE_ITEMS: {
    GET_ALL: (id) => `/calculations/${id}/items`,
    ADD_PACKAGE: (id) => `/calculations/${id}/line-items/package`,
    ADD_CUSTOM: (id) => `/calculations/${id}/line-items/custom`,
  },
  GET_PACKAGES_BY_CATEGORY: (id) => `/calculations/${id}/available-packages`,
}
```

## 9. Hook Dependency Chain Analysis

### Primary Hook Flow

```
CalculationDetailPage
├── useCalculationDetailComplete (main orchestrator)
│   ├── useCalculationDetail (data fetching)
│   │   └── useOptimizedCalculationDetail (performance layer)
│   │       ├── useOptimizedCalculationDetailCore (core data)
│   │       │   └── useParallelCalculationData (API calls)
│   │       ├── useCalculationDetailUI (UI state)
│   │       └── useFinancialCalculations (calculations)
│   ├── useTaxesAndDiscounts (financial state)
│   └── useCalculationActions (user actions)
```

### Data Dependencies

- **Calculation ID**: Flows through entire hook chain
- **User Authentication**: Required for RLS and API auth
- **Currency/City/Venue**: Affects package filtering
- **Line Items**: Triggers financial recalculations
- **Status Changes**: Affects available actions

### Performance Optimizations in Hooks

- **Memoization**: All return objects memoized with stable dependencies
- **Reference Tracking**: Debug utilities track object recreation
- **Conditional Fetching**: Data fetching disabled for invalid IDs
- **Optimistic Updates**: Immediate UI feedback with rollback capability

## 10. Security and Authentication

### Supabase Security Model

- **Row-Level Security (RLS)**: User can only access their own calculations
- **JWT Authentication**: Automatic token handling via Supabase client
- **Database Policies**: Enforced at database level for data isolation

### Backend API Security

- **JWT Validation**: Token verification on each request
- **Role-Based Access**: Admin vs user permissions
- **Request Validation**: DTO validation with class-validator
- **Rate Limiting**: Implemented at API gateway level

### Data Privacy

- **User Isolation**: Calculations filtered by `created_by` field
- **Soft Deletion**: `is_deleted` flag instead of hard deletion
- **Audit Trail**: `created_at`, `updated_at` timestamps on all records

## 11. Error Handling Patterns

### Frontend Error Handling

```typescript
// React Query error handling
const { data, error, isError } = useQuery({
  queryKey: QUERY_KEYS.calculation(id),
  queryFn: () => getCalculationById(id),
  meta: {
    onError: () => {
      toast.error("Failed to load calculation");
    },
  },
});
```

### Service Layer Error Handling

```typescript
// Supabase error handling
try {
  const { data, error } = await supabase.from("table").select();
  if (error) throw error;
  return data;
} catch (error) {
  console.error("Service error:", error);
  showError("User-friendly message");
  throw error;
}
```

### API Error Handling

```typescript
// Backend API error handling
try {
  const response = await authClient.get(endpoint);
  return response.data;
} catch (error) {
  if (error.response?.status === 404) {
    // Handle not found
  } else if (error.response?.status === 403) {
    // Handle unauthorized
  }
  throw error;
}
```

## 12. Conclusion

The calculation detail functionality demonstrates a sophisticated hybrid architecture that balances performance, reliability, and developer experience. The ongoing migration toward consolidated API endpoints shows clear performance benefits while maintaining backward compatibility through intelligent fallback mechanisms.

### Key Strengths

1. **Performance**: Consolidated endpoints reduce API calls by 75%
2. **Reliability**: Graceful degradation ensures system availability
3. **Security**: Multi-layered security with RLS and JWT validation
4. **Developer Experience**: Clear separation of concerns and comprehensive error handling

### Strategic Recommendations

1. **Continue API Consolidation**: Complete migration of remaining Supabase operations
2. **Standardize Patterns**: Establish consistent patterns for new features
3. **Enhance Monitoring**: Add performance metrics and error tracking
4. **Improve Documentation**: Update integration guides and API contracts

This analysis provides a comprehensive foundation for future architectural decisions and API consolidation efforts.
