import {
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import {
  CalculationDetailDto,
  CalculationCustomItemDto,
  CalculationLineItemDto,
  TaxDetailItemDto,
  DiscountDetailDto,
  CityReferenceDto,
  ClientSummaryDto,
  EventSummaryDto,
  CurrencySummaryDto,
  CalculationLineItemOptionDto,
} from '../dto/calculation-detail.dto';
import { VenueReferenceDto } from '../../venues/dto/venue-reference.dto';
import {
  CalculationCustomItemRaw,
  CalculationHistoryRaw,
  CalculationLineItemOptionRaw,
  CalculationLineItemRaw,
  CalculationTaxRaw,
} from '../interfaces/calculation-internal.interfaces';

/**
 * Service responsible for data transformation operations
 * Extracted from the main CalculationsService for better separation of concerns
 */
@Injectable()
export class CalculationTransformationService {
  private readonly logger = new Logger(CalculationTransformationService.name);

  /**
   * Transform raw calculation data to DTO format
   */
  async mapRawToDetailDto(
    raw: CalculationHistoryRaw,
    venues: VenueReferenceDto[] = [],
  ): Promise<CalculationDetailDto> {
    if (!raw || !raw.id) {
      throw new InternalServerErrorException(
        'Invalid raw calculation data for mapping.',
      );
    }

    const currencyDto: CurrencySummaryDto | null = raw.currency
      ? { id: raw.currency.id, code: raw.currency.code }
      : null;

    const cityDto: CityReferenceDto | null =
      raw.city_id && raw.cities?.name
        ? { id: raw.city_id, name: raw.cities.name }
        : null;

    const clientDto: ClientSummaryDto | null = raw.clients
      ? { id: raw.clients.id, client_name: raw.clients.client_name }
      : null;

    const eventDto: EventSummaryDto | null = raw.events
      ? { id: raw.events.id, event_name: raw.events.event_name }
      : null;

    const lineItemsDto: CalculationLineItemDto[] = this.mapLineItems(
      raw.calculation_line_items ?? [],
    );

    const customItemsDto: CalculationCustomItemDto[] = this.mapCustomItems(
      raw.calculation_custom_items ?? [],
    );

    const dto: CalculationDetailDto = {
      id: raw.id,
      name: raw.name,
      currency: currencyDto,
      city: cityDto,
      venues: venues,
      event_start_date: raw.event_start_date,
      event_end_date: raw.event_end_date,
      attendees: raw.attendees,
      event_type_id: raw.event_type_id, // Updated to use event_type_id
      notes: raw.notes,
      version_notes: raw.version_notes,
      status: raw.status,
      created_at: raw.created_at,
      updated_at: raw.updated_at,
      created_by: raw.created_by,
      client: clientDto,
      event: eventDto,
      line_items: lineItemsDto,
      custom_items: customItemsDto,
      subtotal: raw.subtotal ?? 0,
      taxes: this.transformTaxes(raw.calculation_taxes),
      discount: (raw.discount as DiscountDetailDto) ?? null,
      total: raw.total ?? 0,
      total_cost: raw.total_cost ?? 0,
      estimated_profit: raw.estimated_profit ?? 0,
    };

    return dto;
  }

  /**
   * Map raw line items to DTO format
   */
  private mapLineItems(
    rawLineItems: CalculationLineItemRaw[],
  ): CalculationLineItemDto[] {
    return rawLineItems.map(
      (item: CalculationLineItemRaw): CalculationLineItemDto => ({
        id: item.id,
        package_id: item.package_id,
        item_name_snapshot: item.item_name_snapshot,
        option_summary_snapshot: item.option_summary_snapshot,
        item_quantity: item.item_quantity,
        duration_days: item.duration_days,
        unit_base_price: item.unit_base_price,
        options_total_adjustment: item.options_total_adjustment,
        calculated_line_total: item.calculated_line_total,
        notes: item.notes,
        unit_base_cost_snapshot: item.unit_base_cost_snapshot,
        options_total_cost_snapshot: item.options_total_cost_snapshot,
        calculated_line_cost: item.calculated_line_cost,
        options: this.mapLineItemOptions(
          item.calculation_line_item_options ?? [],
        ),
      }),
    );
  }

  /**
   * Map raw line item options to DTO format
   */
  private mapLineItemOptions(
    rawOptions: CalculationLineItemOptionRaw[],
  ): CalculationLineItemOptionDto[] {
    return rawOptions.map(
      (opt: CalculationLineItemOptionRaw): CalculationLineItemOptionDto => ({
        id: opt.option_id,
        option_name_snapshot: opt.package_options?.option_name ?? 'N/A',
        price_adjustment_snapshot: opt.price_adjustment_snapshot,
      }),
    );
  }

  /**
   * Map raw custom items to DTO format
   */
  private mapCustomItems(
    rawCustomItems: CalculationCustomItemRaw[],
  ): CalculationCustomItemDto[] {
    return rawCustomItems.map(
      (item: CalculationCustomItemRaw): CalculationCustomItemDto => ({
        id: item.id,
        item_name: item.item_name,
        description: item.description,
        quantity: item.quantity,
        unit_price: item.unit_price,
        unit_cost: item.unit_cost,
      }),
    );
  }

  /**
   * Transform calculation summary data for template creation
   */
  mapCalculationSummary(calculation: any): any {
    // Transform venues data
    const venues =
      calculation.venues?.map((v: any) => ({
        id: v.venue.id,
        name: v.venue.name,
      })) || [];

    return {
      id: calculation.id,
      name: calculation.name,
      total: calculation.total || 0,
      currency: calculation.currency,
      attendees: calculation.attendees,
      event_type_id: calculation.event_type_id, // Updated to use event_type_id
      city: calculation.city,
      venues: venues,
    };
  }

  /**
   * Transform venue data for calculation summary
   */
  transformVenueData(venues: any[]): VenueReferenceDto[] {
    return venues.map(venue => ({
      id: venue.id,
      name: venue.name,
      address: venue.address,
      city_id: venue.city_id,
      city_name: (venue.cities && venue.cities[0]?.name) || null,
    }));
  }

  /**
   * Validate and sanitize calculation data before transformation
   */
  validateRawData(raw: any): boolean {
    if (!raw) {
      this.logger.error('Raw calculation data is null or undefined');
      return false;
    }

    if (!raw.id) {
      this.logger.error('Raw calculation data missing required ID field');
      return false;
    }

    if (!raw.name) {
      this.logger.error('Raw calculation data missing required name field');
      return false;
    }

    return true;
  }

  /**
   * Sanitize calculation data for safe transformation
   */
  sanitizeRawData(raw: any): CalculationHistoryRaw {
    return {
      ...raw,
      // Ensure required fields have default values
      subtotal: raw.subtotal ?? 0,
      total: raw.total ?? 0,
      total_cost: raw.total_cost ?? 0,
      estimated_profit: raw.estimated_profit ?? 0,
      taxes: raw.calculation_taxes ?? [],
      discount: raw.discount ?? null,
      calculation_line_items: raw.calculation_line_items ?? [],
      calculation_custom_items: raw.calculation_custom_items ?? [],
    };
  }

  /**
   * Transform calculation data for export purposes
   */
  transformForExport(calculation: CalculationDetailDto): any {
    return {
      basic_info: {
        id: calculation.id,
        name: calculation.name,
        status: calculation.status,
        created_at: calculation.created_at,
        updated_at: calculation.updated_at,
      },
      financial: {
        subtotal: calculation.subtotal,
        total: calculation.total,
        total_cost: calculation.total_cost,
        estimated_profit: calculation.estimated_profit,
        currency: calculation.currency,
      },
      event_details: {
        event_start_date: calculation.event_start_date,
        event_end_date: calculation.event_end_date,
        attendees: calculation.attendees,
        event_type_id: calculation.event_type_id, // Updated to use event_type_id
        city: calculation.city,
        venues: calculation.venues,
      },
      items: {
        line_items: calculation.line_items,
        custom_items: calculation.custom_items,
      },
      adjustments: {
        taxes: calculation.taxes,
        discount: calculation.discount,
      },
      relationships: {
        client: calculation.client,
        event: calculation.event,
      },
    };
  }

  /**
   * Transform calculation_taxes array to TaxDetailItemDto array
   */
  private transformTaxes(
    calculationTaxes?: CalculationTaxRaw[],
  ): TaxDetailItemDto[] {
    if (!calculationTaxes || !Array.isArray(calculationTaxes)) {
      return [];
    }

    return calculationTaxes.map(tax => ({
      name: tax.tax_name,
      rate: parseFloat(tax.tax_rate.toString()) || 0,
      amount: parseFloat(tax.tax_amount.toString()) || 0,
    }));
  }
}
