import {
  Injectable,
  Logger,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { SupabaseService } from '../../../core/supabase/supabase.service';
import { User } from '@supabase/supabase-js';
import { CalculationCrudService } from './calculation-crud.service';
import { CalculationItemsService } from '../../calculation-items/calculation-items.service';
import { PackagesService } from '../../packages/packages.service';
import { CategoriesService } from '../../categories/categories.service';
import { CalculationCompleteDataDto } from '../dto/calculation-complete-data.dto';
import { CalculationDetailDto } from '../dto/calculation-detail.dto';
import { LineItemDto } from '../../calculation-items/dto/line-item.dto';
import { CategoryWithPackagesDto } from '../../packages/dto/packages-by-category-response.dto';
import { CategoryDto } from '../../categories/dto/category.dto';

/**
 * Service responsible for providing consolidated calculation data
 * Implements the consolidated endpoint pattern from the API Architecture Migration Plan
 */
@Injectable()
export class CalculationCompleteDataService {
  private readonly logger = new Logger(CalculationCompleteDataService.name);

  constructor(
    private readonly supabaseService: SupabaseService,
    private readonly calculationCrudService: CalculationCrudService,
    private readonly calculationItemsService: CalculationItemsService,
    private readonly packagesService: PackagesService,
    private readonly categoriesService: CategoriesService,
  ) {}

  /**
   * Get complete calculation data in a single API call
   * Replaces the need for 4 separate API calls from the frontend
   *
   * @param calculationId - The calculation ID
   * @param user - The authenticated user
   * @returns Complete calculation data with metadata
   */
  async getCompleteData(
    calculationId: string,
    user: User,
  ): Promise<CalculationCompleteDataDto> {
    this.logger.log(
      `Fetching complete calculation data for ID: ${calculationId}, User: ${user.email}`,
    );

    const startTime = Date.now();

    try {
      // Parallel data fetching with error handling
      const [
        calculationResult,
        lineItemsResult,
        packagesResult,
        categoriesResult,
      ] = await Promise.allSettled([
        this.getCalculationDetails(calculationId, user),
        this.getLineItems(calculationId, user),
        this.getPackagesByCategory(calculationId, user),
        this.getCategories(),
      ]);

      // Handle partial failures gracefully
      const calculation = this.extractResult(calculationResult, 'calculation');
      const lineItems = this.extractResult(lineItemsResult, 'lineItems', []);
      const packages = this.extractResult(packagesResult, 'packages', []);
      const categories = this.extractResult(categoriesResult, 'categories', []);

      const loadTime = Date.now() - startTime;
      const errors = this.collectErrors([
        calculationResult,
        lineItemsResult,
        packagesResult,
        categoriesResult,
      ]);

      const result: CalculationCompleteDataDto = {
        calculation,
        lineItems,
        packages,
        categories,
        metadata: {
          loadTime,
          cacheVersion: '1.0',
          userId: user.id,
          errors,
          timestamp: new Date().toISOString(),
        },
      };

      this.logger.log(
        `Successfully fetched complete calculation data for ID: ${calculationId} in ${loadTime}ms`,
      );

      return result;
    } catch (error) {
      this.logger.error(
        `Failed to fetch complete calculation data for ID: ${calculationId}`,
        error.stack,
      );
      throw new InternalServerErrorException(
        'Failed to load calculation data. Please try again.',
      );
    }
  }

  /**
   * Get calculation details with ownership validation
   */
  private async getCalculationDetails(
    calculationId: string,
    user: User,
  ): Promise<CalculationDetailDto> {
    // First validate ownership
    const rawCalculation =
      await this.calculationCrudService.findCalculationRawById(
        calculationId,
        user,
      );

    // Transform raw calculation to DTO format
    const calculation: CalculationDetailDto = {
      id: rawCalculation.id,
      name: rawCalculation.name,
      status: rawCalculation.status,
      event_type_id: rawCalculation.event_type_id,
      attendees: rawCalculation.attendees,
      event_start_date: rawCalculation.event_start_date,
      event_end_date: rawCalculation.event_end_date,
      notes: rawCalculation.notes,
      version_notes: rawCalculation.version_notes,
      created_at: rawCalculation.created_at,
      updated_at: rawCalculation.updated_at,
      created_by: rawCalculation.created_by,

      // Related entities (simplified for consolidated endpoint)
      currency: rawCalculation.currency
        ? {
            id: rawCalculation.currency.id,
            code: rawCalculation.currency.code,
          }
        : null,
      city: rawCalculation.cities
        ? {
            id: rawCalculation.cities.id,
            name: rawCalculation.cities.name,
          }
        : null,
      client: rawCalculation.clients
        ? {
            id: rawCalculation.clients.id,
            client_name: rawCalculation.clients.client_name,
            contact_person: rawCalculation.clients.contact_person,
            email: rawCalculation.clients.email,
          }
        : null,
      event: rawCalculation.events
        ? {
            id: rawCalculation.events.id,
            event_name: rawCalculation.events.event_name,
          }
        : null,
      venues: [], // Will be populated separately if needed

      // Line items (will be provided separately in consolidated response)
      line_items: [],
      custom_items: [],

      // Financial totals
      subtotal: rawCalculation.subtotal,
      taxes: this.transformTaxes(rawCalculation.calculation_taxes),
      discount: this.transformDiscount(
        rawCalculation.calculation_discounts,
        rawCalculation.discount,
      ),
      total: rawCalculation.total,
      total_cost: rawCalculation.total_cost,
      estimated_profit: rawCalculation.estimated_profit,
    };

    return calculation;
  }

  /**
   * Get line items for the calculation
   */
  private async getLineItems(
    calculationId: string,
    user: User,
  ): Promise<LineItemDto[]> {
    // The calculation items service already validates ownership through the calculation
    return this.calculationItemsService.getCalculationItems(calculationId);
  }

  /**
   * Get packages by category for the calculation
   */
  private async getPackagesByCategory(
    calculationId: string,
    user: User,
  ): Promise<CategoryWithPackagesDto[]> {
    // First get the calculation to extract currency and location info
    const calculation =
      await this.calculationCrudService.findCalculationRawById(
        calculationId,
        user,
      );

    // Get packages by category using the calculation's currency and location
    const packagesResponse = await this.packagesService.getPackagesByCategory(
      calculation.currency_id,
      calculation.city_id || undefined,
      undefined, // venue_id - we'll handle venue filtering separately if needed
      true, // include options
    );

    return packagesResponse.categories;
  }

  /**
   * Get all active categories
   */
  private async getCategories(): Promise<CategoryDto[]> {
    return this.categoriesService.findAll();
  }

  /**
   * Extract result from Promise.allSettled result
   */
  private extractResult<T>(
    result: PromiseSettledResult<T>,
    name: string,
    defaultValue?: T,
  ): T {
    if (result.status === 'fulfilled') {
      return result.value;
    }

    this.logger.warn(
      `Failed to fetch ${name}: ${result.reason?.message || 'Unknown error'}`,
    );

    if (defaultValue !== undefined) {
      return defaultValue;
    }

    // For critical data like calculation, throw error
    if (name === 'calculation') {
      throw result.reason;
    }

    // For non-critical data, return empty array/object
    return [] as unknown as T;
  }

  /**
   * Collect errors from Promise.allSettled results
   */
  private collectErrors(results: PromiseSettledResult<any>[]): string[] {
    return results
      .filter(result => result.status === 'rejected')
      .map(
        result =>
          (result as PromiseRejectedResult).reason?.message || 'Unknown error',
      );
  }

  /**
   * Transform calculation_taxes array to TaxDetailItemDto array
   * Note: Only uses normalized table - no JSONB fallback needed as taxes column doesn't exist
   */
  private transformTaxes(calculationTaxes?: any[]): any[] {
    if (!calculationTaxes || !Array.isArray(calculationTaxes)) {
      return [];
    }

    return calculationTaxes.map(tax => ({
      id: tax.id,
      name: tax.tax_name,
      rate: parseFloat(tax.tax_rate.toString()) || 0,
      amount: parseFloat(tax.tax_amount.toString()) || 0,
      applied_to_subtotal: parseFloat(tax.applied_to_subtotal.toString()) || 0,
      type: 'percentage', // Default type since it's not stored in normalized table
      basis: 'subtotal', // Default basis since it's not stored in normalized table
    }));
  }

  /**
   * Transform calculation_discounts array to DiscountDetailDto, with fallback to legacy JSONB
   */
  private transformDiscount(
    calculationDiscounts?: any[],
    legacyDiscount?: any,
  ): any {
    // Priority 1: Use normalized discount table if available
    if (
      calculationDiscounts &&
      Array.isArray(calculationDiscounts) &&
      calculationDiscounts.length > 0
    ) {
      // For now, we'll take the first discount (assuming single discount per calculation)
      const discount = calculationDiscounts[0];
      return {
        id: discount.id,
        name: discount.discount_name,
        type: discount.discount_type,
        value: parseFloat(discount.discount_value.toString()) || 0,
        amount: parseFloat(discount.discount_amount.toString()) || 0,
        applied_to_subtotal:
          parseFloat(discount.applied_to_subtotal.toString()) || 0,
      };
    }

    // Priority 2: Fallback to legacy JSONB discount
    if (legacyDiscount && typeof legacyDiscount === 'object') {
      return legacyDiscount;
    }

    // Default: No discount
    return null;
  }
}
