export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export type Database = {
  public: {
    Tables: {
      calculation_custom_items: {
        Row: {
          calculation_id: string;
          category_id: string | null;
          city_id: string | null;
          created_at: string;
          currency_id: string;
          description: string | null;
          id: string;
          item_name: string;
          item_quantity: number;
          item_quantity_basis: number | null;
          quantity_basis: string | null;
          unit_cost: number;
          unit_price: number;
          updated_at: string;
        };
        Insert: {
          calculation_id: string;
          category_id?: string | null;
          city_id?: string | null;
          created_at?: string;
          currency_id: string;
          description?: string | null;
          id?: string;
          item_name: string;
          item_quantity?: number;
          item_quantity_basis?: number | null;
          quantity_basis?: string | null;
          unit_cost?: number;
          unit_price: number;
          updated_at?: string;
        };
        Update: {
          calculation_id?: string;
          category_id?: string | null;
          city_id?: string | null;
          created_at?: string;
          currency_id?: string;
          description?: string | null;
          id?: string;
          item_name?: string;
          item_quantity?: number;
          item_quantity_basis?: number | null;
          quantity_basis?: string | null;
          unit_cost?: number;
          unit_price?: number;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "calc_custom_items_calc_id_fkey";
            columns: ["calculation_id"];
            isOneToOne: false;
            referencedRelation: "calculation_history";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "calc_custom_items_category_id_fkey";
            columns: ["category_id"];
            isOneToOne: false;
            referencedRelation: "categories";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "calc_custom_items_city_id_fkey";
            columns: ["city_id"];
            isOneToOne: false;
            referencedRelation: "cities";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "calc_custom_items_currency_id_fkey";
            columns: ["currency_id"];
            isOneToOne: false;
            referencedRelation: "currencies";
            referencedColumns: ["id"];
          }
        ];
      };
      calculation_history: {
        Row: {
          attendees: number | null;
          city_id: string | null;
          client_id: string | null;
          created_at: string;
          created_by: string;
          currency_id: string;
          deleted_at: string | null;
          discount: Json | null;
          estimated_profit: number;
          event_end_date: string | null;
          event_id: string | null;
          event_start_date: string | null;
          event_type_id: string | null;
          id: string;
          is_deleted: boolean;
          name: string;
          notes: string | null;
          status: Database["public"]["Enums"]["calculation_status"];
          subtotal: number;
          taxes: Json | null;
          total: number;
          total_cost: number;
          updated_at: string;
          version_notes: string | null;
        };
        Insert: {
          attendees?: number | null;
          city_id?: string | null;
          client_id?: string | null;
          created_at?: string;
          created_by: string;
          currency_id: string;
          deleted_at?: string | null;
          discount?: Json | null;
          estimated_profit?: number;
          event_end_date?: string | null;
          event_id?: string | null;
          event_start_date?: string | null;
          event_type_id?: string | null;
          id?: string;
          is_deleted?: boolean;
          name: string;
          notes?: string | null;
          status?: Database["public"]["Enums"]["calculation_status"];
          subtotal?: number;
          taxes?: Json | null;
          total?: number;
          total_cost?: number;
          updated_at?: string;
          version_notes?: string | null;
        };
        Update: {
          attendees?: number | null;
          city_id?: string | null;
          client_id?: string | null;
          created_at?: string;
          created_by?: string;
          currency_id?: string;
          deleted_at?: string | null;
          discount?: Json | null;
          estimated_profit?: number;
          event_end_date?: string | null;
          event_id?: string | null;
          event_start_date?: string | null;
          event_type_id?: string | null;
          id?: string;
          is_deleted?: boolean;
          name?: string;
          notes?: string | null;
          status?: Database["public"]["Enums"]["calculation_status"];
          subtotal?: number;
          taxes?: Json | null;
          total?: number;
          total_cost?: number;
          updated_at?: string;
          version_notes?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "calculation_history_city_id_fkey";
            columns: ["city_id"];
            isOneToOne: false;
            referencedRelation: "cities";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "calculation_history_client_id_fkey";
            columns: ["client_id"];
            isOneToOne: false;
            referencedRelation: "clients";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "calculation_history_currency_id_fkey";
            columns: ["currency_id"];
            isOneToOne: false;
            referencedRelation: "currencies";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "calculation_history_event_id_fkey";
            columns: ["event_id"];
            isOneToOne: false;
            referencedRelation: "events";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "calculation_history_event_type_id_fkey";
            columns: ["event_type_id"];
            isOneToOne: false;
            referencedRelation: "event_types";
            referencedColumns: ["id"];
          }
        ];
      };
      calculation_line_item_options: {
        Row: {
          cost_adjustment_snapshot: number;
          currency_id: string;
          id: string;
          line_item_id: string;
          option_id: string;
          price_adjustment_snapshot: number;
        };
        Insert: {
          cost_adjustment_snapshot?: number;
          currency_id: string;
          id?: string;
          line_item_id: string;
          option_id: string;
          price_adjustment_snapshot: number;
        };
        Update: {
          cost_adjustment_snapshot?: number;
          currency_id?: string;
          id?: string;
          line_item_id?: string;
          option_id?: string;
          price_adjustment_snapshot?: number;
        };
        Relationships: [
          {
            foreignKeyName: "calc_line_item_options_currency_id_fkey";
            columns: ["currency_id"];
            isOneToOne: false;
            referencedRelation: "currencies";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "calc_line_item_options_line_item_id_fkey";
            columns: ["line_item_id"];
            isOneToOne: false;
            referencedRelation: "calculation_line_items";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "calc_line_item_options_option_id_fkey";
            columns: ["option_id"];
            isOneToOne: false;
            referencedRelation: "package_options";
            referencedColumns: ["id"];
          }
        ];
      };
      calculation_line_items: {
        Row: {
          calculated_line_cost: number;
          calculated_line_total: number;
          calculation_id: string;
          created_at: string;
          currency_id: string;
          id: string;
          item_name_snapshot: string;
          item_quantity: number;
          item_quantity_basis: number;
          notes: string | null;
          option_summary_snapshot: string | null;
          options_total_adjustment: number;
          options_total_cost_snapshot: number;
          package_id: string | null;
          quantity_basis: string | null;
          unit_base_cost_snapshot: number;
          unit_base_price: number;
          updated_at: string;
        };
        Insert: {
          calculated_line_cost?: number;
          calculated_line_total?: number;
          calculation_id: string;
          created_at?: string;
          currency_id: string;
          id?: string;
          item_name_snapshot: string;
          item_quantity?: number;
          item_quantity_basis?: number;
          notes?: string | null;
          option_summary_snapshot?: string | null;
          options_total_adjustment?: number;
          options_total_cost_snapshot?: number;
          package_id?: string | null;
          quantity_basis?: string | null;
          unit_base_cost_snapshot?: number;
          unit_base_price?: number;
          updated_at?: string;
        };
        Update: {
          calculated_line_cost?: number;
          calculated_line_total?: number;
          calculation_id?: string;
          created_at?: string;
          currency_id?: string;
          id?: string;
          item_name_snapshot?: string;
          item_quantity?: number;
          item_quantity_basis?: number;
          notes?: string | null;
          option_summary_snapshot?: string | null;
          options_total_adjustment?: number;
          options_total_cost_snapshot?: number;
          package_id?: string | null;
          quantity_basis?: string | null;
          unit_base_cost_snapshot?: number;
          unit_base_price?: number;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "calc_line_items_calc_id_fkey";
            columns: ["calculation_id"];
            isOneToOne: false;
            referencedRelation: "calculation_history";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "calc_line_items_currency_id_fkey";
            columns: ["currency_id"];
            isOneToOne: false;
            referencedRelation: "currencies";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "calc_line_items_package_id_fkey";
            columns: ["package_id"];
            isOneToOne: false;
            referencedRelation: "packages";
            referencedColumns: ["id"];
          }
        ];
      };
      calculation_venues: {
        Row: {
          calculation_id: string;
          created_at: string;
          id: string;
          updated_at: string;
          venue_id: string;
        };
        Insert: {
          calculation_id: string;
          created_at?: string;
          id?: string;
          updated_at?: string;
          venue_id: string;
        };
        Update: {
          calculation_id?: string;
          created_at?: string;
          id?: string;
          updated_at?: string;
          venue_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "calculation_venues_calculation_id_fkey";
            columns: ["calculation_id"];
            isOneToOne: false;
            referencedRelation: "calculation_history";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "calculation_venues_venue_id_fkey";
            columns: ["venue_id"];
            isOneToOne: false;
            referencedRelation: "venues";
            referencedColumns: ["id"];
          }
        ];
      };
      categories: {
        Row: {
          code: string;
          created_at: string;
          description: string | null;
          display_order: number | null;
          icon: string | null;
          id: string;
          name: string;
          updated_at: string;
        };
        Insert: {
          code: string;
          created_at?: string;
          description?: string | null;
          display_order?: number | null;
          icon?: string | null;
          id?: string;
          name: string;
          updated_at?: string;
        };
        Update: {
          code?: string;
          created_at?: string;
          description?: string | null;
          display_order?: number | null;
          icon?: string | null;
          id?: string;
          name?: string;
          updated_at?: string;
        };
        Relationships: [];
      };
      cities: {
        Row: {
          created_at: string;
          id: string;
          name: string;
          updated_at: string;
        };
        Insert: {
          created_at?: string;
          id?: string;
          name: string;
          updated_at?: string;
        };
        Update: {
          created_at?: string;
          id?: string;
          name?: string;
          updated_at?: string;
        };
        Relationships: [];
      };
      clients: {
        Row: {
          address: string | null;
          client_name: string;
          company_name: string | null;
          contact_person: string | null;
          created_at: string;
          email: string | null;
          id: string;
          phone: string | null;
          updated_at: string;
        };
        Insert: {
          address?: string | null;
          client_name: string;
          company_name?: string | null;
          contact_person?: string | null;
          created_at?: string;
          email?: string | null;
          id?: string;
          phone?: string | null;
          updated_at?: string;
        };
        Update: {
          address?: string | null;
          client_name?: string;
          company_name?: string | null;
          contact_person?: string | null;
          created_at?: string;
          email?: string | null;
          id?: string;
          phone?: string | null;
          updated_at?: string;
        };
        Relationships: [];
      };
      currencies: {
        Row: {
          code: string;
          created_at: string;
          description: string | null;
          id: string;
          updated_at: string;
        };
        Insert: {
          code: string;
          created_at?: string;
          description?: string | null;
          id?: string;
          updated_at?: string;
        };
        Update: {
          code?: string;
          created_at?: string;
          description?: string | null;
          id?: string;
          updated_at?: string;
        };
        Relationships: [];
      };
      divisions: {
        Row: {
          code: string;
          created_at: string;
          description: string | null;
          id: string;
          is_active: boolean;
          name: string;
          updated_at: string;
        };
        Insert: {
          code: string;
          created_at?: string;
          description?: string | null;
          id?: string;
          is_active?: boolean;
          name: string;
          updated_at?: string;
        };
        Update: {
          code?: string;
          created_at?: string;
          description?: string | null;
          id?: string;
          is_active?: boolean;
          name?: string;
          updated_at?: string;
        };
        Relationships: [];
      };
      event_types: {
        Row: {
          code: string;
          color: string | null;
          created_at: string | null;
          description: string | null;
          display_order: number | null;
          icon: string | null;
          id: string;
          is_active: boolean | null;
          name: string;
          updated_at: string | null;
        };
        Insert: {
          code: string;
          color?: string | null;
          created_at?: string | null;
          description?: string | null;
          display_order?: number | null;
          icon?: string | null;
          id?: string;
          is_active?: boolean | null;
          name: string;
          updated_at?: string | null;
        };
        Update: {
          code?: string;
          color?: string | null;
          created_at?: string | null;
          description?: string | null;
          display_order?: number | null;
          icon?: string | null;
          id?: string;
          is_active?: boolean | null;
          name?: string;
          updated_at?: string | null;
        };
        Relationships: [];
      };
      events: {
        Row: {
          client_id: string | null;
          created_at: string;
          deleted_at: string | null;
          event_end_datetime: string | null;
          event_name: string;
          event_start_datetime: string | null;
          id: string;
          is_deleted: boolean;
          notes: string | null;
          primary_contact_id: string | null;
          status: Database["public"]["Enums"]["event_status"];
          updated_at: string;
          venue_details: string | null;
        };
        Insert: {
          client_id?: string | null;
          created_at?: string;
          deleted_at?: string | null;
          event_end_datetime?: string | null;
          event_name: string;
          event_start_datetime?: string | null;
          id?: string;
          is_deleted?: boolean;
          notes?: string | null;
          primary_contact_id?: string | null;
          status?: Database["public"]["Enums"]["event_status"];
          updated_at?: string;
          venue_details?: string | null;
        };
        Update: {
          client_id?: string | null;
          created_at?: string;
          deleted_at?: string | null;
          event_end_datetime?: string | null;
          event_name?: string;
          event_start_datetime?: string | null;
          id?: string;
          is_deleted?: boolean;
          notes?: string | null;
          primary_contact_id?: string | null;
          status?: Database["public"]["Enums"]["event_status"];
          updated_at?: string;
          venue_details?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "events_client_id_fkey";
            columns: ["client_id"];
            isOneToOne: false;
            referencedRelation: "clients";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "events_primary_contact_profiles_fkey";
            columns: ["primary_contact_id"];
            isOneToOne: false;
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          }
        ];
      };
      export_history: {
        Row: {
          calculation_id: string;
          completed_at: string | null;
          created_at: string;
          created_by: string;
          error_message: string | null;
          export_type: Database["public"]["Enums"]["export_type"];
          file_name: string | null;
          file_size_bytes: number | null;
          id: string;
          mime_type: string | null;
          recipient: string | null;
          status: Database["public"]["Enums"]["export_status"];
          storage_path: string | null;
        };
        Insert: {
          calculation_id: string;
          completed_at?: string | null;
          created_at?: string;
          created_by: string;
          error_message?: string | null;
          export_type?: Database["public"]["Enums"]["export_type"];
          file_name?: string | null;
          file_size_bytes?: number | null;
          id?: string;
          mime_type?: string | null;
          recipient?: string | null;
          status?: Database["public"]["Enums"]["export_status"];
          storage_path?: string | null;
        };
        Update: {
          calculation_id?: string;
          completed_at?: string | null;
          created_at?: string;
          created_by?: string;
          error_message?: string | null;
          export_type?: Database["public"]["Enums"]["export_type"];
          file_name?: string | null;
          file_size_bytes?: number | null;
          id?: string;
          mime_type?: string | null;
          recipient?: string | null;
          status?: Database["public"]["Enums"]["export_status"];
          storage_path?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "export_history_calculation_id_fkey";
            columns: ["calculation_id"];
            isOneToOne: false;
            referencedRelation: "calculation_history";
            referencedColumns: ["id"];
          }
        ];
      };
      migration_log: {
        Row: {
          column_name: string;
          executed_at: string | null;
          id: number;
          migration_id: string;
          new_data_type: string;
          old_data_type: string;
          records_affected: number | null;
          status: string | null;
          table_name: string;
        };
        Insert: {
          column_name: string;
          executed_at?: string | null;
          id?: number;
          migration_id: string;
          new_data_type: string;
          old_data_type: string;
          records_affected?: number | null;
          status?: string | null;
          table_name: string;
        };
        Update: {
          column_name?: string;
          executed_at?: string | null;
          id?: number;
          migration_id?: string;
          new_data_type?: string;
          old_data_type?: string;
          records_affected?: number | null;
          status?: string | null;
          table_name?: string;
        };
        Relationships: [];
      };
      package_cities: {
        Row: {
          city_id: string;
          created_at: string;
          id: string;
          package_id: string;
        };
        Insert: {
          city_id: string;
          created_at?: string;
          id?: string;
          package_id: string;
        };
        Update: {
          city_id?: string;
          created_at?: string;
          id?: string;
          package_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "package_cities_city_id_fkey";
            columns: ["city_id"];
            isOneToOne: false;
            referencedRelation: "cities";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "package_cities_package_id_fkey";
            columns: ["package_id"];
            isOneToOne: false;
            referencedRelation: "packages";
            referencedColumns: ["id"];
          }
        ];
      };
      package_dependencies: {
        Row: {
          created_at: string;
          dependency_type: string;
          dependent_package_id: string;
          description: string | null;
          id: string;
          package_id: string;
          updated_at: string;
        };
        Insert: {
          created_at?: string;
          dependency_type: string;
          dependent_package_id: string;
          description?: string | null;
          id?: string;
          package_id: string;
          updated_at?: string;
        };
        Update: {
          created_at?: string;
          dependency_type?: string;
          dependent_package_id?: string;
          description?: string | null;
          id?: string;
          package_id?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "package_dependencies_dependent_package_id_fkey";
            columns: ["dependent_package_id"];
            isOneToOne: false;
            referencedRelation: "packages";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "package_dependencies_package_id_fkey";
            columns: ["package_id"];
            isOneToOne: false;
            referencedRelation: "packages";
            referencedColumns: ["id"];
          }
        ];
      };
      package_options: {
        Row: {
          applicable_package_id: string;
          cost_adjustment: number;
          created_at: string;
          currency_id: string;
          description: string | null;
          id: string;
          is_default_for_package: boolean;
          is_required: boolean;
          option_code: string;
          option_group: string | null;
          option_name: string;
          price_adjustment: number;
          updated_at: string;
        };
        Insert: {
          applicable_package_id: string;
          cost_adjustment?: number;
          created_at?: string;
          currency_id: string;
          description?: string | null;
          id?: string;
          is_default_for_package?: boolean;
          is_required?: boolean;
          option_code: string;
          option_group?: string | null;
          option_name: string;
          price_adjustment?: number;
          updated_at?: string;
        };
        Update: {
          applicable_package_id?: string;
          cost_adjustment?: number;
          created_at?: string;
          currency_id?: string;
          description?: string | null;
          id?: string;
          is_default_for_package?: boolean;
          is_required?: boolean;
          option_code?: string;
          option_group?: string | null;
          option_name?: string;
          price_adjustment?: number;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "package_options_applicable_package_id_fkey";
            columns: ["applicable_package_id"];
            isOneToOne: false;
            referencedRelation: "packages";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "package_options_currency_id_fkey";
            columns: ["currency_id"];
            isOneToOne: false;
            referencedRelation: "currencies";
            referencedColumns: ["id"];
          }
        ];
      };
      package_prices: {
        Row: {
          created_at: string;
          currency_id: string;
          description: string | null;
          id: string;
          package_id: string;
          price: number;
          unit_base_cost: number;
          updated_at: string;
        };
        Insert: {
          created_at?: string;
          currency_id: string;
          description?: string | null;
          id?: string;
          package_id: string;
          price: number;
          unit_base_cost?: number;
          updated_at?: string;
        };
        Update: {
          created_at?: string;
          currency_id?: string;
          description?: string | null;
          id?: string;
          package_id?: string;
          price?: number;
          unit_base_cost?: number;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "package_prices_currency_id_fkey";
            columns: ["currency_id"];
            isOneToOne: false;
            referencedRelation: "currencies";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "package_prices_package_id_fkey";
            columns: ["package_id"];
            isOneToOne: false;
            referencedRelation: "packages";
            referencedColumns: ["id"];
          }
        ];
      };
      package_venues: {
        Row: {
          created_at: string | null;
          deleted_at: string | null;
          id: string;
          is_deleted: boolean | null;
          package_id: string | null;
          venue_id: string | null;
        };
        Insert: {
          created_at?: string | null;
          deleted_at?: string | null;
          id?: string;
          is_deleted?: boolean | null;
          package_id?: string | null;
          venue_id?: string | null;
        };
        Update: {
          created_at?: string | null;
          deleted_at?: string | null;
          id?: string;
          is_deleted?: boolean | null;
          package_id?: string | null;
          venue_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "package_venues_package_id_fkey";
            columns: ["package_id"];
            isOneToOne: false;
            referencedRelation: "packages";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "package_venues_venue_id_fkey";
            columns: ["venue_id"];
            isOneToOne: false;
            referencedRelation: "venues";
            referencedColumns: ["id"];
          }
        ];
      };
      packages: {
        Row: {
          category_id: string | null;
          created_at: string;
          deleted_at: string | null;
          description: string | null;
          division_id: string | null;
          id: string;
          is_deleted: boolean;
          name: string;
          quantity_basis: Database["public"]["Enums"]["package_quantity_basis"];
          seq_number: number;
          updated_at: string;
          variation_group_code: string | null;
        };
        Insert: {
          category_id?: string | null;
          created_at?: string;
          deleted_at?: string | null;
          description?: string | null;
          division_id?: string | null;
          id?: string;
          is_deleted?: boolean;
          name: string;
          quantity_basis?: Database["public"]["Enums"]["package_quantity_basis"];
          seq_number?: number;
          updated_at?: string;
          variation_group_code?: string | null;
        };
        Update: {
          category_id?: string | null;
          created_at?: string;
          deleted_at?: string | null;
          description?: string | null;
          division_id?: string | null;
          id?: string;
          is_deleted?: boolean;
          name?: string;
          quantity_basis?: Database["public"]["Enums"]["package_quantity_basis"];
          seq_number?: number;
          updated_at?: string;
          variation_group_code?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "packages_category_id_fkey";
            columns: ["category_id"];
            isOneToOne: false;
            referencedRelation: "categories";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "packages_division_id_fkey";
            columns: ["division_id"];
            isOneToOne: false;
            referencedRelation: "divisions";
            referencedColumns: ["id"];
          }
        ];
      };
      profiles: {
        Row: {
          address: string | null;
          city: string | null;
          company_name: string | null;
          created_at: string;
          full_name: string | null;
          id: string;
          phone_number: string | null;
          preferences: Json | null;
          profile_picture_url: string | null;
          role_id: number | null;
          updated_at: string;
          username: string;
        };
        Insert: {
          address?: string | null;
          city?: string | null;
          company_name?: string | null;
          created_at?: string;
          full_name?: string | null;
          id: string;
          phone_number?: string | null;
          preferences?: Json | null;
          profile_picture_url?: string | null;
          role_id?: number | null;
          updated_at?: string;
          username: string;
        };
        Update: {
          address?: string | null;
          city?: string | null;
          company_name?: string | null;
          created_at?: string;
          full_name?: string | null;
          id?: string;
          phone_number?: string | null;
          preferences?: Json | null;
          profile_picture_url?: string | null;
          role_id?: number | null;
          updated_at?: string;
          username?: string;
        };
        Relationships: [
          {
            foreignKeyName: "fk_role";
            columns: ["role_id"];
            isOneToOne: false;
            referencedRelation: "roles";
            referencedColumns: ["id"];
          }
        ];
      };
      roles: {
        Row: {
          created_at: string;
          description: string | null;
          id: number;
          role_name: string;
          updated_at: string;
        };
        Insert: {
          created_at?: string;
          description?: string | null;
          id?: never;
          role_name: string;
          updated_at?: string;
        };
        Update: {
          created_at?: string;
          description?: string | null;
          id?: never;
          role_name?: string;
          updated_at?: string;
        };
        Relationships: [];
      };
      template_categories: {
        Row: {
          created_at: string;
          created_by: string;
          description: string | null;
          id: string;
          name: string;
          updated_at: string;
        };
        Insert: {
          created_at?: string;
          created_by: string;
          description?: string | null;
          id?: string;
          name: string;
          updated_at?: string;
        };
        Update: {
          created_at?: string;
          created_by?: string;
          description?: string | null;
          id?: string;
          name?: string;
          updated_at?: string;
        };
        Relationships: [];
      };
      template_venues: {
        Row: {
          created_at: string;
          id: string;
          template_id: string;
          updated_at: string;
          venue_id: string;
        };
        Insert: {
          created_at?: string;
          id?: string;
          template_id: string;
          updated_at?: string;
          venue_id: string;
        };
        Update: {
          created_at?: string;
          id?: string;
          template_id?: string;
          updated_at?: string;
          venue_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "template_venues_template_id_fkey";
            columns: ["template_id"];
            isOneToOne: false;
            referencedRelation: "templates";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "template_venues_venue_id_fkey";
            columns: ["venue_id"];
            isOneToOne: false;
            referencedRelation: "venues";
            referencedColumns: ["id"];
          }
        ];
      };
      templates: {
        Row: {
          attendees: number | null;
          category_id: string | null;
          city_id: string | null;
          created_at: string;
          created_by: string;
          currency_id: string | null;
          custom_items: Json | null;
          deleted_at: string | null;
          description: string | null;
          discount: Json | null;
          event_type: string | null;
          event_type_id: string | null;
          id: string;
          is_deleted: boolean;
          is_public: boolean;
          name: string;
          package_selections: Json;
          taxes: Json | null;
          template_end_date: string | null;
          template_start_date: string | null;
          updated_at: string;
        };
        Insert: {
          attendees?: number | null;
          category_id?: string | null;
          city_id?: string | null;
          created_at?: string;
          created_by: string;
          currency_id?: string | null;
          custom_items?: Json | null;
          deleted_at?: string | null;
          description?: string | null;
          discount?: Json | null;
          event_type?: string | null;
          event_type_id?: string | null;
          id?: string;
          is_deleted?: boolean;
          is_public?: boolean;
          name: string;
          package_selections: Json;
          taxes?: Json | null;
          template_end_date?: string | null;
          template_start_date?: string | null;
          updated_at?: string;
        };
        Update: {
          attendees?: number | null;
          category_id?: string | null;
          city_id?: string | null;
          created_at?: string;
          created_by?: string;
          currency_id?: string | null;
          custom_items?: Json | null;
          deleted_at?: string | null;
          description?: string | null;
          discount?: Json | null;
          event_type?: string | null;
          event_type_id?: string | null;
          id?: string;
          is_deleted?: boolean;
          is_public?: boolean;
          name?: string;
          package_selections?: Json;
          taxes?: Json | null;
          template_end_date?: string | null;
          template_start_date?: string | null;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "templates_category_id_fkey";
            columns: ["category_id"];
            isOneToOne: false;
            referencedRelation: "template_categories";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "templates_city_id_fkey";
            columns: ["city_id"];
            isOneToOne: false;
            referencedRelation: "cities";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "templates_currency_id_fkey";
            columns: ["currency_id"];
            isOneToOne: false;
            referencedRelation: "currencies";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "templates_currency_id_fkey1";
            columns: ["currency_id"];
            isOneToOne: false;
            referencedRelation: "currencies";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "templates_event_type_id_fkey";
            columns: ["event_type_id"];
            isOneToOne: false;
            referencedRelation: "event_types";
            referencedColumns: ["id"];
          }
        ];
      };
      templates_backup_001: {
        Row: {
          attendees: number | null;
          category_id: string | null;
          city_id: string | null;
          created_at: string | null;
          created_by: string | null;
          currency_id: string | null;
          custom_items: Json | null;
          deleted_at: string | null;
          description: string | null;
          discount: Json | null;
          event_type: string | null;
          id: string | null;
          is_deleted: boolean | null;
          is_public: boolean | null;
          name: string | null;
          package_selections: Json | null;
          taxes: Json | null;
          template_end_date: string | null;
          template_start_date: string | null;
          updated_at: string | null;
        };
        Insert: {
          attendees?: number | null;
          category_id?: string | null;
          city_id?: string | null;
          created_at?: string | null;
          created_by?: string | null;
          currency_id?: string | null;
          custom_items?: Json | null;
          deleted_at?: string | null;
          description?: string | null;
          discount?: Json | null;
          event_type?: string | null;
          id?: string | null;
          is_deleted?: boolean | null;
          is_public?: boolean | null;
          name?: string | null;
          package_selections?: Json | null;
          taxes?: Json | null;
          template_end_date?: string | null;
          template_start_date?: string | null;
          updated_at?: string | null;
        };
        Update: {
          attendees?: number | null;
          category_id?: string | null;
          city_id?: string | null;
          created_at?: string | null;
          created_by?: string | null;
          currency_id?: string | null;
          custom_items?: Json | null;
          deleted_at?: string | null;
          description?: string | null;
          discount?: Json | null;
          event_type?: string | null;
          id?: string | null;
          is_deleted?: boolean | null;
          is_public?: boolean | null;
          name?: string | null;
          package_selections?: Json | null;
          taxes?: Json | null;
          template_end_date?: string | null;
          template_start_date?: string | null;
          updated_at?: string | null;
        };
        Relationships: [];
      };
      token_blacklist: {
        Row: {
          created_at: string | null;
          expires_at: string;
          id: string;
          reason: string | null;
          revoked_by: string | null;
          token_hash: string;
        };
        Insert: {
          created_at?: string | null;
          expires_at: string;
          id?: string;
          reason?: string | null;
          revoked_by?: string | null;
          token_hash: string;
        };
        Update: {
          created_at?: string | null;
          expires_at?: string;
          id?: string;
          reason?: string | null;
          revoked_by?: string | null;
          token_hash?: string;
        };
        Relationships: [];
      };
      venues: {
        Row: {
          address: string | null;
          capacity: number | null;
          city_id: string | null;
          classification: string | null;
          created_at: string | null;
          deleted_at: string | null;
          description: string | null;
          features: Json | null;
          id: string;
          image_url: string | null;
          is_active: boolean | null;
          is_deleted: boolean | null;
          name: string;
          updated_at: string | null;
        };
        Insert: {
          address?: string | null;
          capacity?: number | null;
          city_id?: string | null;
          classification?: string | null;
          created_at?: string | null;
          deleted_at?: string | null;
          description?: string | null;
          features?: Json | null;
          id?: string;
          image_url?: string | null;
          is_active?: boolean | null;
          is_deleted?: boolean | null;
          name: string;
          updated_at?: string | null;
        };
        Update: {
          address?: string | null;
          capacity?: number | null;
          city_id?: string | null;
          classification?: string | null;
          created_at?: string | null;
          deleted_at?: string | null;
          description?: string | null;
          features?: Json | null;
          id?: string;
          image_url?: string | null;
          is_active?: boolean | null;
          is_deleted?: boolean | null;
          name?: string;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "venues_city_id_fkey";
            columns: ["city_id"];
            isOneToOne: false;
            referencedRelation: "cities";
            referencedColumns: ["id"];
          }
        ];
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      add_package_item_and_recalculate: {
        Args: {
          p_calculation_id: string;
          p_user_id: string;
          p_package_id: string;
          p_option_ids: string[];
          p_currency_id: string;
          p_quantity_override?: number;
          p_duration_override?: number;
          p_notes?: string;
        };
        Returns: string;
      };
      begin_transaction: {
        Args: Record<PropertyKey, never>;
        Returns: undefined;
      };
      commit_transaction: {
        Args: Record<PropertyKey, never>;
        Returns: undefined;
      };
      delete_line_item_and_recalculate: {
        Args: {
          p_calculation_id: string;
          p_item_id: string;
          p_user_id: string;
          p_item_type: string;
        };
        Returns: undefined;
      };
      execute_sql: {
        Args: { sql_query: string };
        Returns: undefined;
      };
      get_batch_package_options: {
        Args: {
          p_package_ids: string[];
          p_currency_id: string;
          p_venue_id?: string;
        };
        Returns: {
          package_id: string;
          option_id: string;
          option_name: string;
          description: string;
          price_adjustment: number;
          cost_adjustment: number;
          is_default_for_package: boolean;
          is_required: boolean;
        }[];
      };
      get_packages_by_category_with_availability: {
        Args: {
          p_currency_id: string;
          p_city_id?: string;
          p_venue_id?: string;
          p_category_id?: string;
        };
        Returns: {
          id: string;
          name: string;
          description: string;
          category_id: string;
          category_name: string;
          category_display_order: number;
          quantity_basis: string;
          price: number;
          unit_base_cost: number;
          is_available_in_city: boolean;
          is_available_in_venue: boolean;
        }[];
      };
      get_packages_with_prices: {
        Args: { package_ids: string[] };
        Returns: {
          id: string;
          name: string;
          description: string;
          quantity_basis: string;
          package_prices: Json;
        }[];
      };
      get_user_accessible_template_by_id: {
        Args: { p_template_id: string; p_user_id: string };
        Returns: {
          id: string;
          name: string;
          description: string;
          event_type: string;
          city_id: string;
          attendees: number;
          template_start_date: string;
          template_end_date: string;
          package_selections: Json;
          category_id: string;
          created_at: string;
          updated_at: string;
          created_by: string;
          is_public: boolean;
        }[];
      };
      get_user_accessible_templates: {
        Args: {
          p_user_id: string;
          p_search?: string;
          p_event_type?: string;
          p_city_id?: string;
          p_category_id?: string;
          p_date_start?: string;
          p_date_end?: string;
          p_sort_by?: string;
          p_sort_order?: string;
          p_limit?: number;
          p_offset?: number;
        };
        Returns: {
          id: string;
          name: string;
          description: string;
          event_type: string;
          city_id: string;
          attendees: number;
          template_start_date: string;
          template_end_date: string;
          package_selections: Json;
          category_id: string;
          created_at: string;
          updated_at: string;
          created_by: string;
          is_public: boolean;
          total_count: number;
        }[];
      };
      migrate_event_type_strings: {
        Args: Record<PropertyKey, never>;
        Returns: {
          table_name: string;
          records_updated: number;
          unmapped_values: string[];
        }[];
      };
      recalculate_calculation_totals: {
        Args: { p_calculation_id: string };
        Returns: undefined;
      };
      rollback_transaction: {
        Args: Record<PropertyKey, never>;
        Returns: undefined;
      };
    };
    Enums: {
      calculation_status: "draft" | "completed" | "canceled";
      event_status:
        | "LEAD"
        | "PLANNING"
        | "CONFIRMED"
        | "IN_PROGRESS"
        | "COMPLETED"
        | "POST_EVENT"
        | "CANCELLED"
        | "ON_HOLD";
      export_status: "PENDING" | "PROCESSING" | "COMPLETED" | "FAILED";
      export_type: "xlsx" | "pdf" | "email" | "csv";
      package_quantity_basis:
        | "PER_EVENT"
        | "PER_DAY"
        | "PER_ATTENDEE"
        | "PER_ITEM"
        | "PER_ITEM_PER_DAY"
        | "PER_ATTENDEE_PER_DAY";
    };
    CompositeTypes: {
      package_option_details_type: {
        id: string | null;
        option_name: string | null;
        price_adjustment: number | null;
        cost_adjustment: number | null;
      };
    };
  };
};

type DefaultSchema = Database[Extract<keyof Database, "public">];

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R;
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
      DefaultSchema["Views"])
  ? (DefaultSchema["Tables"] &
      DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
      Row: infer R;
    }
    ? R
    : never
  : never;

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I;
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
  ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
      Insert: infer I;
    }
    ? I
    : never
  : never;

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U;
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
  ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
      Update: infer U;
    }
    ? U
    : never
  : never;

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
  ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
  : never;

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
  ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
  : never;

export const Constants = {
  public: {
    Enums: {
      calculation_status: ["draft", "completed", "canceled"],
      event_status: [
        "LEAD",
        "PLANNING",
        "CONFIRMED",
        "IN_PROGRESS",
        "COMPLETED",
        "POST_EVENT",
        "CANCELLED",
        "ON_HOLD",
      ],
      export_status: ["PENDING", "PROCESSING", "COMPLETED", "FAILED"],
      export_type: ["xlsx", "pdf", "email", "csv"],
      package_quantity_basis: [
        "PER_EVENT",
        "PER_DAY",
        "PER_ATTENDEE",
        "PER_ITEM",
        "PER_ITEM_PER_DAY",
        "PER_ATTENDEE_PER_DAY",
      ],
    },
  },
} as const;
