/**
 * Consolidated calculation data service
 *
 * This service implements the consolidated endpoint pattern from the API Architecture Migration Plan.
 * It replaces the need for 4 separate API calls with a single consolidated endpoint.
 */

import { getAuthenticatedApiClient } from "@/integrations/api/client";
import { API_ENDPOINTS } from "@/integrations/api/endpoints";
import { showError } from "@/lib/notifications";
import { LineItem, QuantityBasisEnum } from "@/types/calculation";

/**
 * Types for the consolidated response
 */
export interface CalculationCompleteData {
  calculation: {
    id: string;
    name: string;
    status: string;
    event_type_id: string | null;
    attendees: number | null;
    event_start_date: string | null;
    event_end_date: string | null;
    notes: string | null;
    version_notes: string | null;
    created_at: string;
    updated_at: string;
    created_by: string;
    currency: {
      id: string;
      code: string;
    } | null;
    city: {
      id: string;
      name: string;
    } | null;
    client: {
      id: string;
      client_name: string;
      contact_person?: string | null;
      email?: string | null;
    } | null;
    event: {
      id: string;
      event_name: string;
    } | null;
    venues: any[];
    line_items: any[];
    custom_items: any[];
    subtotal: number;
    taxes: any;
    discount: any;
    total: number;
    total_cost: number;
    estimated_profit: number;
  };
  lineItems: LineItem[];
  packages: Array<{
    id: string;
    name: string;
    display_order: number;
    packages: Array<{
      id: string;
      name: string;
      description: string;
      quantity_basis: string;
      price: number;
      unit_base_cost: number;
      options?: Array<{
        id: string;
        option_name: string;
        description: string;
        price_adjustment: number;
        cost_adjustment: number;
        is_default_for_package: boolean;
        is_required: boolean;
      }>;
    }>;
  }>;
  categories: Array<{
    id: string;
    name: string;
    display_order: number;
    is_deleted: boolean;
    created_at: string;
    updated_at: string;
  }>;
  metadata: {
    loadTime: number;
    cacheVersion: string;
    userId: string;
    errors?: string[];
    timestamp: string;
  };
}

/**
 * Fetch complete calculation data in a single API call
 * This replaces the need for 4 separate API calls:
 * 1. GET /calculations/:id (calculation details)
 * 2. GET /calculations/:id/items (line items)
 * 3. GET /calculations/:id/available-packages (packages by category)
 * 4. GET /categories (categories)
 *
 * @param calculationId - The calculation ID
 * @returns Complete calculation data with metadata
 */
export const getCompleteCalculationData = async (
  calculationId: string
): Promise<CalculationCompleteData> => {
  try {
    console.log(
      `[API] Fetching complete calculation data for ID: ${calculationId}`
    );
    const startTime = Date.now();

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make single API request to get all data
    const response = await authClient.get(
      API_ENDPOINTS.CALCULATIONS.GET_COMPLETE_DATA(calculationId)
    );

    const data = response.data as CalculationCompleteData;

    // Transform line items to match frontend format
    const transformedLineItems: LineItem[] = data.lineItems.map(
      (item: any) => ({
        id: item.id,
        calculation_id: calculationId,
        package_id: item.package_id || undefined,
        name: item.is_custom ? item.item_name : item.item_name_snapshot,
        description: item.is_custom
          ? item.description
          : item.notes || undefined,
        quantity: item.is_custom ? item.item_quantity : item.item_quantity,
        item_quantity_basis: item.is_custom
          ? item.item_quantity_basis || 1
          : item.item_quantity_basis || item.duration_days || 1,
        unit_price: item.is_custom ? item.unit_price : item.unit_base_price,
        total_price: item.is_custom
          ? item.item_quantity * item.unit_price
          : item.calculated_line_total,
        category_id: item.category_id || "",
        is_custom: item.is_custom,
        quantity_basis:
          item.quantity_basis && typeof item.quantity_basis === "string"
            ? QuantityBasisEnum[
                item.quantity_basis as keyof typeof QuantityBasisEnum
              ] || QuantityBasisEnum.PER_DAY
            : QuantityBasisEnum.PER_DAY,
        selectedOptions:
          item.options?.map((option: any) => option.option_id) || [],
        created_at: item.created_at,
        updated_at: item.updated_at,
        createdAt: new Date(item.created_at),
        updatedAt: new Date(item.updated_at),
      })
    );

    const loadTime = Date.now() - startTime;

    console.log(`[API] Fetched complete calculation data in ${loadTime}ms:`, {
      calculation: data.calculation.name,
      lineItems: transformedLineItems.length,
      packages: data.packages.length,
      categories: data.categories.length,
      errors: data.metadata.errors?.length || 0,
    });

    return {
      ...data,
      lineItems: transformedLineItems,
    };
  } catch (error) {
    console.error(
      `[API] Error fetching complete calculation data for ID ${calculationId}:`,
      error
    );
    showError("Failed to load calculation data", {
      description:
        "There was an error loading the calculation data. Please try again.",
    });
    throw error;
  }
};

/**
 * Check if the consolidated endpoint is available
 * Since the endpoint is now stable, we always return true
 * This maintains backward compatibility while removing unnecessary test requests
 *
 * @returns Promise<boolean> - Always true (endpoint is available)
 */
export const isConsolidatedEndpointAvailable = async (): Promise<boolean> => {
  // The consolidated endpoint is now stable and always available
  // No need to make test requests that cause 500 errors in logs
  return true;
};

/**
 * Performance comparison utility
 * Compares the performance of consolidated vs separate API calls
 *
 * @param calculationId - The calculation ID to test with
 * @returns Performance comparison data
 */
export const compareApiPerformance = async (calculationId: string) => {
  const authClient = await getAuthenticatedApiClient();

  // Test consolidated endpoint
  const consolidatedStart = Date.now();
  try {
    await authClient.get(
      API_ENDPOINTS.CALCULATIONS.GET_COMPLETE_DATA(calculationId)
    );
    const consolidatedTime = Date.now() - consolidatedStart;

    // Test separate endpoints
    const separateStart = Date.now();
    await Promise.all([
      authClient.get(API_ENDPOINTS.CALCULATIONS.GET_BY_ID(calculationId)),
      authClient.get(
        API_ENDPOINTS.CALCULATIONS.LINE_ITEMS.GET_ALL(calculationId)
      ),
      authClient.get(
        API_ENDPOINTS.CALCULATIONS.GET_PACKAGES_BY_CATEGORY(calculationId)
      ),
      authClient.get(API_ENDPOINTS.CATEGORIES.GET_ALL),
    ]);
    const separateTime = Date.now() - separateStart;

    return {
      consolidated: consolidatedTime,
      separate: separateTime,
      improvement:
        (((separateTime - consolidatedTime) / separateTime) * 100).toFixed(1) +
        "%",
      requestsReduced: "4 → 1",
    };
  } catch (error) {
    console.error("[API] Performance comparison failed:", error);
    return null;
  }
};
