import {
  Injectable,
  Logger,
  InternalServerErrorException,
  NotFoundException,
  ForbiddenException,
} from '@nestjs/common';
import { SupabaseClient, User, PostgrestError } from '@supabase/supabase-js';
import { SupabaseService } from '../../core/supabase/supabase.service';
import { AddPackageLineItemDto } from './dto/add-package-line-item.dto';
import { AddCustomLineItemDto } from './dto/add-custom-line-item.dto';
import { UpdateLineItemDto } from './dto/update-line-item.dto';
import { LineItemDto } from './dto/line-item.dto';

// Re-define or import necessary interfaces/types if needed
// Especially PackageSelectionItem from calculations.service
interface PackageSelectionItem {
  package_id: string;
  option_ids: string[];
  item_quantity: number | null;
  item_quantity_basis: number | null;
}

// Interface for fetched package data
interface PackageDetails {
  id: string;
  name: string;
  quantity_basis: 'attendees' | 'fixed' | 'days' | 'attendees_days';
  // Add other fields if needed
}

// Interface for fetched price data
interface PackagePrice {
  price: number;
  unit_base_cost: number;
}

// Interface for fetched option data including price/cost adjustment
interface PackageOptionDetails {
  id: string;
  option_name: string;
  price_adjustment: number; // For the specific currency
  cost_adjustment: number; // For the specific currency
}

@Injectable()
export class CalculationItemsService {
  private readonly logger = new Logger(CalculationItemsService.name);

  constructor(private readonly supabaseService: SupabaseService) {
    this.logger.log('Using optimized v2 RPC functions');
  }

  /**
   * Get all line items for a calculation
   * @param calculationId The calculation ID
   * @returns Array of line items
   */
  async getCalculationItems(calculationId: string): Promise<LineItemDto[]> {
    this.logger.log(`Fetching line items for calculation ID: ${calculationId}`);
    const supabase = this.supabaseService.getClient();

    try {
      // First, get all package line items
      const { data: packageItems, error: packageError } = await supabase
        .from('calculation_line_items')
        .select(
          `
          id,
          calculation_id,
          package_id,
          item_name_snapshot,
          notes,
          item_quantity,
          item_quantity_basis,
          quantity_basis,
          unit_base_price,
          calculated_line_total,
          created_at,
          updated_at,
          calculation_line_item_options (
            id,
            option_id,
            price_adjustment_snapshot,
            package_options (
              option_name
            )
          )
        `,
        )
        .eq('calculation_id', calculationId)
        .order('created_at', { ascending: true });

      if (packageError) {
        this.logger.error(
          `Error fetching package items: ${packageError.message}`,
        );
        throw new InternalServerErrorException(
          'Failed to fetch package line items',
        );
      }

      // Then, get all custom line items
      const { data: customItems, error: customError } = await supabase
        .from('calculation_custom_items')
        .select(
          `
          id,
          calculation_id,
          item_name,
          description,
          item_quantity,
          item_quantity_basis,
          quantity_basis,
          unit_price,
          unit_cost,
          category_id,
          created_at,
          updated_at
        `,
        )
        .eq('calculation_id', calculationId)
        .order('created_at', { ascending: true });

      if (customError) {
        this.logger.error(
          `Error fetching custom items: ${customError.message}`,
        );
        throw new InternalServerErrorException(
          'Failed to fetch custom line items',
        );
      }

      // Transform package items to match the LineItemDto format
      const transformedPackageItems = packageItems.map(item => ({
        id: item.id,
        calculation_id: item.calculation_id,
        package_id: item.package_id,
        item_name: item.item_name_snapshot,
        item_name_snapshot: item.item_name_snapshot,
        description: null,
        notes: item.notes,
        quantity: item.item_quantity,
        item_quantity: item.item_quantity,
        item_quantity_basis: item.item_quantity_basis, // Add missing field
        duration_days: item.item_quantity_basis,
        quantity_basis: item.quantity_basis, // Add missing field
        unit_price: null,
        unit_base_price: item.unit_base_price,
        calculated_line_total: item.calculated_line_total,
        category_id: null, // category_id is not available in the calculation_line_items table
        is_custom: false,
        options:
          item.calculation_line_item_options?.map(option => ({
            option_id: option.option_id,
            // Access option_name through the package_options relationship
            option_name: option.package_options?.[0]?.option_name,
          })) || [],
        created_at: item.created_at,
        updated_at: item.updated_at,
      }));

      // Transform custom items to match the LineItemDto format
      const transformedCustomItems = customItems.map(item => ({
        id: item.id,
        calculation_id: item.calculation_id,
        package_id: null,
        item_name: item.item_name,
        item_name_snapshot: null,
        description: item.description,
        notes: null,
        quantity: item.item_quantity, // Use item_quantity from database
        item_quantity: item.item_quantity, // Use item_quantity from database
        item_quantity_basis: item.item_quantity_basis, // Add missing field
        duration_days: item.item_quantity_basis, // Use item_quantity_basis from database
        quantity_basis: item.quantity_basis, // Add missing field
        unit_price: item.unit_price,
        unit_base_price: null,
        calculated_line_total:
          item.item_quantity * item.unit_price * item.item_quantity_basis, // Use consistent calculation
        category_id: item.category_id,
        is_custom: true,
        options: [],
        created_at: item.created_at,
        updated_at: item.updated_at,
      }));

      // Combine and return all items
      const allItems = [...transformedPackageItems, ...transformedCustomItems];

      // Sort by created_at
      allItems.sort(
        (a, b) =>
          new Date(a.created_at).getTime() - new Date(b.created_at).getTime(),
      );

      this.logger.log(
        `Fetched ${allItems.length} line items for calculation ID: ${calculationId}`,
      );
      return allItems;
    } catch (error) {
      this.logger.error(`Error in getCalculationItems: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get a specific line item by ID
   * @param calculationId The calculation ID
   * @param itemId The line item ID
   * @returns The line item
   */
  async getLineItemById(
    calculationId: string,
    itemId: string,
  ): Promise<LineItemDto> {
    this.logger.log(
      `Fetching line item ID: ${itemId} for calculation ID: ${calculationId}`,
    );
    const supabase = this.supabaseService.getClient();

    try {
      // First, try to find it as a package item
      const { data: packageItem, error: packageError } = await supabase
        .from('calculation_line_items')
        .select(
          `
          id,
          calculation_id,
          package_id,
          item_name_snapshot,
          notes,
          item_quantity,
          item_quantity_basis,
          quantity_basis,
          unit_base_price,
          calculated_line_total,
          created_at,
          updated_at,
          calculation_line_item_options (
            id,
            option_id,
            price_adjustment_snapshot,
            package_options (
              option_name
            )
          )
        `,
        )
        .eq('id', itemId)
        .eq('calculation_id', calculationId)
        .single();

      if (!packageError && packageItem) {
        // It's a package item
        return {
          id: packageItem.id,
          calculation_id: packageItem.calculation_id,
          package_id: packageItem.package_id,
          item_name: packageItem.item_name_snapshot,
          item_name_snapshot: packageItem.item_name_snapshot,
          description: null,
          notes: packageItem.notes,
          quantity: packageItem.item_quantity,
          item_quantity: packageItem.item_quantity,
          item_quantity_basis: packageItem.item_quantity_basis, // Add missing field
          duration_days: packageItem.item_quantity_basis,
          quantity_basis: packageItem.quantity_basis, // Add missing field
          unit_price: null,
          unit_base_price: packageItem.unit_base_price,
          calculated_line_total: packageItem.calculated_line_total,
          category_id: null, // category_id is not available in the calculation_line_items table
          is_custom: false,
          options:
            packageItem.calculation_line_item_options?.map(option => ({
              option_id: option.option_id,
              // Access option_name through the package_options relationship
              option_name: option.package_options?.[0]?.option_name,
            })) || [],
          created_at: packageItem.created_at,
          updated_at: packageItem.updated_at,
        };
      }

      // If not found as a package item, try as a custom item
      const { data: customItem, error: customError } = await supabase
        .from('calculation_custom_items')
        .select(
          `
          id,
          calculation_id,
          item_name,
          description,
          item_quantity,
          item_quantity_basis,
          quantity_basis,
          unit_price,
          unit_cost,
          category_id,
          created_at,
          updated_at
        `,
        )
        .eq('id', itemId)
        .eq('calculation_id', calculationId)
        .single();

      if (customError) {
        this.logger.error(`Error fetching line item: ${customError.message}`);
        throw new NotFoundException(`Line item with ID ${itemId} not found`);
      }

      if (!customItem) {
        throw new NotFoundException(`Line item with ID ${itemId} not found`);
      }

      // It's a custom item
      return {
        id: customItem.id,
        calculation_id: customItem.calculation_id,
        package_id: null,
        item_name: customItem.item_name,
        item_name_snapshot: null,
        description: customItem.description,
        notes: null,
        quantity: customItem.item_quantity, // Use item_quantity from database
        item_quantity: customItem.item_quantity, // Use item_quantity from database
        item_quantity_basis: customItem.item_quantity_basis, // Add missing field
        duration_days: customItem.item_quantity_basis, // Use item_quantity_basis from database
        quantity_basis: customItem.quantity_basis, // Add missing field
        unit_price: customItem.unit_price,
        unit_base_price: null,
        calculated_line_total:
          customItem.item_quantity *
          customItem.unit_price *
          customItem.item_quantity_basis, // Use consistent calculation
        category_id: customItem.category_id,
        is_custom: true,
        options: [],
        created_at: customItem.created_at,
        updated_at: customItem.updated_at,
      };
    } catch (error) {
      this.logger.error(`Error in getLineItemById: ${error.message}`);
      throw error;
    }
  }

  /**
   * Populates calculation line items and options based on a template blueprint.
   * Fetches current package/option data and prices/costs for snapshotting.
   * @param calculationId The ID of the newly created calculation_history record.
   * @param currencyId The currency ID for fetching prices/costs.
   * @param packageSelections The blueprint array from the source template.
   * @param user The user object for ownership checks and RPC calls.
   */
  async populateItemsFromTemplateBlueprint(
    calculationId: string,
    currencyId: string,
    packageSelections: PackageSelectionItem[],
    user: User,
  ): Promise<void> {
    this.logger.log(
      `Populating items for calculation ${calculationId} (Currency: ${currencyId}). User: ${user.id}`,
    );
    const supabase = this.supabaseService.getClient();

    for (const item of packageSelections) {
      this.logger.log(
        `Processing package ${item.package_id} for calc ${calculationId} via RPC.`,
      );

      this.logger.debug(`Processing item: ${JSON.stringify(item)}`);

      try {
        // Prepare parameters for the RPC call
        const rpcParams = {
          p_calculation_id: calculationId,
          p_user_id: user.id,
          p_package_id: item.package_id,
          p_option_ids: item.option_ids ?? [],
          p_currency_id: currencyId,
          p_quantity_override: item.item_quantity,
          p_duration_override: item.item_quantity_basis,
          p_notes: `Added from template blueprint`,
        };

        this.logger.debug(
          `Calling RPC add_package_item_and_recalculate with params: ${JSON.stringify(rpcParams)}`,
        );

        // Use optimized v2 RPC function
        const { data: newLineItemId, error: rpcError } = await supabase.rpc(
          'add_package_item_v2',
          rpcParams,
        );

        if (rpcError) {
          this.logger.error(
            `RPC add_package_item_and_recalculate failed during template population for package ${item.package_id} on calc ${calculationId}: ${rpcError.message}`,
            rpcError.details,
          );
          // Decide if failure for one item should stop the whole process
          // For now, logging error and continuing with the next item
          // Consider collecting errors and returning them or throwing a single error at the end.
          continue; // Skip to the next item
        }

        if (!newLineItemId) {
          this.logger.error(
            `RPC add_package_item_and_recalculate for package ${item.package_id} did not return a line item ID during template population.`,
          );
          // Decide how critical this is - continue or throw?
          continue;
        }

        this.logger.log(
          `Successfully processed package ${item.package_id} via RPC -> line item ${newLineItemId}`,
        );
      } catch (error) {
        // Catch any unexpected errors during the loop iteration
        const message =
          error instanceof Error ? error.message : 'Unknown error';
        const stack = error instanceof Error ? error.stack : undefined;
        this.logger.error(
          `Unexpected error processing package ${item.package_id} for calc ${calculationId} during template population: ${message}`,
          stack,
        );
        // Decide: re-throw, log and continue, or collect errors?
        // Re-throwing stops the whole process.
        throw new InternalServerErrorException(
          `An unexpected error occurred while processing package ${item.package_id}.`,
        );
      }
    }

    this.logger.log(
      `Finished populating items for calculation ${calculationId}.`,
    );
    // Recalculation is now handled within the RPC for each item.
    // The final recalculation call in CalculationsService.createFromTemplate might still be needed
    // if there are other steps after item population, or as a final catch-all.
    // For now, assuming RPC handles it sufficiently per item.
  }

  // --- Helper Data Fetching Methods ---

  private async fetchPackageDetails(
    supabase: SupabaseClient,
    packageId: string,
  ): Promise<PackageDetails> {
    const { data, error } = await supabase
      .from('packages')
      .select('id, name, quantity_basis')
      .eq('id', packageId)
      .single();

    if (error || !data) {
      this.logger.error(
        `Package details not found for ID ${packageId}: ${error?.message}`,
      );
      throw new NotFoundException(
        `Required package details not found for ID ${packageId}.`,
      );
    }

    return data;
  }

  private async fetchPackagePrice(
    supabase: SupabaseClient,
    packageId: string,
    currencyId: string,
  ): Promise<PackagePrice> {
    const { data, error } = await supabase
      .from('package_prices')
      .select('price, unit_base_cost')
      .eq('package_id', packageId)
      .eq('currency_id', currencyId)
      .maybeSingle(); // Use maybeSingle as price might not exist for currency

    if (error) {
      this.logger.error(
        `Error fetching price for package ${packageId}, currency ${currencyId}: ${error.message}`,
      );
      throw new InternalServerErrorException(
        `Database error fetching price for package ${packageId}.`,
      );
    }
    if (!data) {
      this.logger.warn(
        `Price not found for package ${packageId}, currency ${currencyId}. Defaulting to 0.`,
      );
      // Decide: Throw error or default to 0? Defaulting might hide issues.
      // throw new NotFoundException(`Price not found for package ${packageId} in currency ${currencyId}.`);
      return { price: 0, unit_base_cost: 0 }; // Defaulting to 0
    }

    return data;
  }

  private async fetchOptionDetails(
    supabase: SupabaseClient,
    optionIds: string[],
    currencyId: string,
  ): Promise<PackageOptionDetails[]> {
    if (!optionIds || optionIds.length === 0) {
      return [];
    }

    const { data, error } = await supabase
      .from('package_options')
      .select('id, option_name, price_adjustment, cost_adjustment')
      .in('id', optionIds)
      .eq('currency_id', currencyId);

    if (error) {
      this.logger.error(
        `Error fetching options [${optionIds.join(', ')}] for currency ${currencyId}: ${error.message}`,
      );
      throw new InternalServerErrorException(
        `Database error fetching option details.`,
      );
    }
    if (!data || data.length !== optionIds.length) {
      this.logger.warn(
        `Mismatch fetching options for IDs [${optionIds.join(', ')}] and currency ${currencyId}. Some options might be missing or lack price data for this currency.`,
      );
      // Decide how to handle missing options or price data - throw error or filter?
      // Throwing an error might be safer to ensure calculation integrity.
      throw new NotFoundException(
        `One or more options [${optionIds.join(', ')}] not found or missing price data for currency ${currencyId}.`,
      );
    }

    return data;
  }

  // --- Method Stubs ---

  async addPackageLineItem(
    calcId: string,
    addDto: AddPackageLineItemDto,
    user: User,
  ): Promise<{ id: string }> {
    this.logger.log(
      `User ${user.id} adding package ${addDto.packageId} to calc ${calcId} via RPC.`,
    );
    const supabase = this.supabaseService.getClient();

    // 1. Check ownership
    await this.checkCalculationOwnership(supabase, calcId, user.id);

    // 2. Fetch calculation currency (needed for RPC)
    const { data: calcData, error: calcError } = await supabase
      .from('calculation_history')
      .select('currency_id')
      .eq('id', calcId)
      .single<{ currency_id: string }>();

    if (calcError || !calcData) {
      this.logger.error(
        `Error fetching calculation ${calcId} before adding item: ${calcError?.message}`,
      );
      throw new InternalServerErrorException(
        'Could not fetch calculation details.',
      );
    }
    // Ownership is checked above by checkCalculationOwnership
    // if (calcData.created_by !== user.id) {
    //   throw new ForbiddenException('User does not own this calculation.');
    // }

    const currencyId = calcData.currency_id;

    // 3. Prepare parameters for the RPC call
    const rpcParams = {
      p_calculation_id: calcId,
      p_user_id: user.id,
      p_package_id: addDto.packageId,
      p_option_ids: addDto.optionIds ?? [],
      p_currency_id: currencyId,
      p_quantity_override: addDto.quantity,
      p_duration_override: addDto.duration,
      p_notes: addDto.notes,
    };

    this.logger.debug(
      `Calling RPC add_package_item_and_recalculate with params: ${JSON.stringify(rpcParams)}`,
    );

    // 4. Call the optimized v2 RPC function
    // The RPC handles snapshotting, item/option insertion, and recalculation
    const { data: newLineItemId, error: rpcError } = await supabase.rpc(
      'add_package_item_v2',
      rpcParams,
    );

    if (rpcError) {
      this.logger.error(
        `RPC add_package_item_v2 failed for calc ${calcId}: ${rpcError.message}`,
        rpcError.details, // Log details which might contain the underlying SQL error
      );
      // Check for specific errors raised by the function
      if (rpcError.message.includes('Calculation not found')) {
        throw new NotFoundException(`Calculation with ID ${calcId} not found.`);
      }
      if (rpcError.message.includes('does not own calculation')) {
        throw new ForbiddenException('User does not own this calculation.');
      }
      if (rpcError.message.includes('Package not found')) {
        throw new NotFoundException(
          `Package ID ${addDto.packageId} not found.`,
        );
      }
      if (rpcError.message.includes('Price not found')) {
        throw new NotFoundException(
          `Price not found for package ${addDto.packageId} and currency ${currencyId}.`,
        );
      }
      if (rpcError.message.includes('options not found')) {
        throw new NotFoundException(
          `One or more options not found for package ${addDto.packageId} and currency ${currencyId}.`,
        );
      }
      // --- TODO: Add check for dependency/conflict errors if added back to RPC ---

      // Generic fallback
      throw new InternalServerErrorException(
        'Failed to add package line item via RPC.',
      );
    }

    if (!newLineItemId) {
      this.logger.error(
        `RPC add_package_item_and_recalculate for calc ${calcId} did not return a line item ID.`,
      );
      throw new InternalServerErrorException(
        'Failed to get new line item ID after adding package.',
      );
    }

    this.logger.log(
      `Successfully added package ${addDto.packageId} via RPC. New line item ID: ${newLineItemId}`,
    );

    // The RPC function now handles the recalculation internally
    // No need to call this.recalcCalculation(calcId); here anymore.

    return { id: newLineItemId };
  }

  async addCustomLineItem(
    calcId: string,
    addDto: AddCustomLineItemDto,
    user: User,
  ): Promise<{ id: string }> {
    this.logger.log(
      `User ${user.id} adding custom item '${addDto.itemName}' to calculation ${calcId}`,
    );
    const supabase = this.supabaseService.getClient();

    // 1. Check ownership
    await this.checkCalculationOwnership(supabase, calcId, user.id);

    // 2. Get calculation currency (needed? Custom item DTO might include it)
    // Fetching calc currency might be redundant if DTO is self-contained
    const { data: calcData, error: calcError } = await supabase
      .from('calculation_history')
      .select('currency_id')
      .eq('id', calcId)
      .single<{ currency_id: string }>();

    if (calcError || !calcData) {
      throw new InternalServerErrorException(
        'Failed to retrieve calculation currency for custom item.',
      );
    }

    // 3. Prepare insert payload
    const insertPayload = {
      calculation_id: calcId,
      item_name: addDto.itemName,
      description: addDto.description,
      item_quantity: addDto.quantity, // Map quantity to item_quantity
      item_quantity_basis: addDto.itemQuantityBasis || 1, // Add item_quantity_basis
      quantity_basis: addDto.quantityBasis || 'PER_DAY', // Add quantity_basis
      unit_price: addDto.unitPrice,
      unit_cost: addDto.unitCost || 0,
      currency_id: calcData.currency_id, // Use calculation's currency
      category_id: addDto.categoryId, // Optional: Add if needed
      city_id: addDto.cityId, // Optional: Add if needed
    };

    // 4. Insert the custom item
    const insertResponse = await supabase
      .from('calculation_custom_items')
      .insert(insertPayload)
      .select('id') // Select only the ID
      .single(); // Remove generic here

    const { data, error: insertError } = insertResponse;
    // Explicitly cast the data after the call
    const newItem = data as { id: string } | null;

    if (insertError || !newItem) {
      this.logger.error(
        `Failed to add custom item to calculation ${calcId}: ${insertError?.message}`,
        insertError?.stack,
      );
      throw new InternalServerErrorException('Could not add custom item.');
    }

    // 5. Trigger recalculation
    await this.recalcCalculationViaRpc(calcId);

    this.logger.log(
      `Custom item ${newItem.id} added to calculation ${calcId} and recalculation triggered.`,
    );
    return { id: newItem.id };
  }

  async updateLineItem(
    calcId: string,
    itemId: string,
    updateDto: UpdateLineItemDto,
    user: User,
  ): Promise<LineItemDto> {
    this.logger.log(
      `Attempting to update line item ${itemId} in calc ${calcId}`,
    );
    const supabase = this.supabaseService.getClient();

    // First, get the current line item to determine if it's package or custom
    const currentItem = await this.getLineItemById(calcId, itemId);

    if (currentItem.is_custom) {
      // Update custom item
      this.logger.log(`Updating custom item ${itemId}`);

      const updatePayload: any = {};
      if (updateDto.itemName !== undefined)
        updatePayload.item_name = updateDto.itemName;
      if (updateDto.description !== undefined)
        updatePayload.description = updateDto.description;
      if (updateDto.quantity !== undefined)
        updatePayload.item_quantity = updateDto.quantity;
      if (updateDto.unitPrice !== undefined)
        updatePayload.unit_price = updateDto.unitPrice;
      if (updateDto.unitCost !== undefined)
        updatePayload.unit_cost = updateDto.unitCost;
      if (updateDto.itemQuantityBasis !== undefined)
        updatePayload.item_quantity_basis = updateDto.itemQuantityBasis;
      if (updateDto.quantityBasis !== undefined)
        updatePayload.quantity_basis = updateDto.quantityBasis;
      if (updateDto.categoryId !== undefined)
        updatePayload.category_id = updateDto.categoryId;

      // Add updated timestamp
      updatePayload.updated_at = new Date().toISOString();

      const { data, error } = await supabase
        .from('calculation_custom_items')
        .update(updatePayload)
        .eq('id', itemId)
        .eq('calculation_id', calcId)
        .select('*')
        .single();

      if (error) {
        this.logger.error(
          `Error updating custom item ${itemId}: ${error.message}`,
          error.details,
        );
        throw new InternalServerErrorException('Failed to update custom item.');
      }

      this.logger.log(`Successfully updated custom item ${itemId}`);
    } else {
      // Update package line item
      this.logger.log(`Updating package line item ${itemId}`);

      const updatePayload: any = {};
      if (updateDto.quantity !== undefined)
        updatePayload.item_quantity = updateDto.quantity;
      if (updateDto.duration_days !== undefined)
        updatePayload.item_quantity_basis = updateDto.duration_days;
      if (updateDto.notes !== undefined) updatePayload.notes = updateDto.notes;
      if (updateDto.description !== undefined)
        updatePayload.notes = updateDto.description; // Map description to notes for package items

      // Add updated timestamp
      updatePayload.updated_at = new Date().toISOString();

      const { data, error } = await supabase
        .from('calculation_line_items')
        .update(updatePayload)
        .eq('id', itemId)
        .eq('calculation_id', calcId)
        .select('*')
        .single();

      if (error) {
        this.logger.error(
          `Error updating package line item ${itemId}: ${error.message}`,
          error.details,
        );
        throw new InternalServerErrorException(
          'Failed to update package line item.',
        );
      }

      this.logger.log(`Successfully updated package line item ${itemId}`);
    }

    // Trigger recalculation
    await this.recalcCalculationViaRpc(calcId);

    // Return the updated item
    return this.getLineItemById(calcId, itemId);
  }

  async deletePackageLineItem(
    calcId: string,
    itemId: string,
    user: User,
  ): Promise<void> {
    this.logger.log(
      `Attempting to delete package item ${itemId} from calc ${calcId} via RPC`,
    );
    const supabase = this.supabaseService.getClient();

    // Prepare parameters for the RPC call
    const rpcParams = {
      p_calculation_id: calcId,
      p_item_id: itemId,
      p_user_id: user.id,
      p_item_type: 'package',
    };

    this.logger.debug(
      `Calling RPC delete_line_item_v2 with params: ${JSON.stringify(rpcParams)}`,
    );

    const { error } = await supabase.rpc('delete_line_item_v2', rpcParams);

    if (error) {
      this.logger.error(
        `RPC delete_line_item_v2 failed for package item ${itemId} on calc ${calcId}: ${error.message}`,
        error.details,
      );
      // Handle specific errors if needed (e.g., ownership, not found)
      if (error.message.includes('does not own calculation')) {
        throw new ForbiddenException('User does not own this calculation.');
      }
      if (
        error.message.includes('Calculation not found') ||
        error.message.includes('line item not found')
      ) {
        throw new NotFoundException('Calculation or line item not found.');
      }
      throw new InternalServerErrorException(
        'Failed to delete package line item.',
      );
    }

    this.logger.log(
      `Successfully deleted package item ${itemId} from calc ${calcId} via RPC.`,
    );
  }

  async deleteCustomLineItem(
    calcId: string,
    itemId: string,
    user: User,
  ): Promise<void> {
    this.logger.log(
      `Attempting to delete custom item ${itemId} from calc ${calcId} via RPC`,
    );
    const supabase = this.supabaseService.getClient();

    // Prepare parameters for the RPC call
    const rpcParams = {
      p_calculation_id: calcId,
      p_item_id: itemId,
      p_user_id: user.id,
      p_item_type: 'custom',
    };

    this.logger.debug(
      `Calling RPC delete_line_item_v2 with params: ${JSON.stringify(rpcParams)}`,
    );

    const { error } = await supabase.rpc('delete_line_item_v2', rpcParams);

    if (error) {
      this.logger.error(
        `RPC delete_line_item_v2 failed for custom item ${itemId} on calc ${calcId}: ${error.message}`,
        error.details,
      );
      // Handle specific errors
      if (error.message.includes('does not own calculation')) {
        throw new ForbiddenException('User does not own this calculation.');
      }
      if (
        error.message.includes('Calculation not found') ||
        error.message.includes('line item not found')
      ) {
        throw new NotFoundException('Calculation or line item not found.');
      }
      throw new InternalServerErrorException(
        'Failed to delete custom line item.',
      );
    }

    this.logger.log(
      `Successfully deleted custom item ${itemId} from calc ${calcId} via RPC.`,
    );
  }

  // --- Private Helper Methods ---

  private async checkCalculationOwnership(
    supabase: SupabaseClient,
    calculationId: string,
    userId: string,
  ): Promise<void> {
    const { data, error } = await supabase
      .from('calculation_history')
      .select('id')
      .eq('id', calculationId)
      .eq('created_by', userId)
      .maybeSingle();

    if (error) {
      this.logger.error(
        `Error checking ownership for calc ${calculationId}: ${error.message}`,
      );
      throw new InternalServerErrorException('Ownership check failed.');
    }
    if (!data) {
      this.logger.warn(
        `User ${userId} attempt to modify calc ${calculationId} without ownership.`,
      );
      throw new ForbiddenException('Access denied to this calculation.');
    }
  }

  /**
   * Calls the optimized v2 database function to recalculate totals for a given calculation.
   * @param calcId The ID of the calculation to recalculate.
   */
  private async recalcCalculationViaRpc(calcId: string): Promise<void> {
    this.logger.log(
      `Triggering recalculation RPC v2 for calculation ${calcId}`,
    );
    const supabase = this.supabaseService.getClient();

    const { error: rpcError } = await supabase.rpc(
      'recalculate_calculation_totals_v2',
      {
        p_calculation_id: calcId,
      },
    );

    if (rpcError) {
      this.logger.error(
        `RPC recalculate_calculation_totals_v2 failed for calc ${calcId}: ${rpcError.message}`,
        rpcError.details,
      );
      // Re-throw the error so the calling method knows recalculation failed
      throw new InternalServerErrorException(
        `Failed to trigger recalculation for calculation ${calcId}.`,
      );
    }

    this.logger.log(
      `Successfully triggered recalculation via RPC v2 for calc ID: ${calcId}`,
    );
  }

  /**
   * Delete a line item (package or custom)
   * @param calculationId The calculation ID
   * @param lineItemId The line item ID
   * @param user The authenticated user
   */
  async deleteLineItem(
    calculationId: string,
    lineItemId: string,
    user: User,
  ): Promise<void> {
    this.logger.log(
      `Deleting line item ${lineItemId} from calculation ${calculationId}`,
    );
    const supabase = this.supabaseService.getClient();

    try {
      // First, try to delete as a package line item
      const { error: packageDeleteError } = await supabase
        .from('calculation_line_items')
        .delete()
        .eq('id', lineItemId)
        .eq('calculation_id', calculationId);

      if (!packageDeleteError) {
        this.logger.log(`Package line item ${lineItemId} deleted successfully`);
        return;
      }

      // If not found as package item, try to delete as custom item
      const { error: customDeleteError } = await supabase
        .from('calculation_custom_items')
        .delete()
        .eq('id', lineItemId)
        .eq('calculation_id', calculationId);

      if (customDeleteError) {
        this.logger.error(
          `Error deleting line item ${lineItemId}: ${customDeleteError.message}`,
        );
        throw new NotFoundException(
          `Line item with ID ${lineItemId} not found`,
        );
      }

      this.logger.log(`Custom line item ${lineItemId} deleted successfully`);
    } catch (error) {
      this.logger.error(`Error in deleteLineItem: ${error.message}`);
      throw error;
    }
  }

  // Make public and refine logic --- THIS METHOD IS NOW DEPRECATED / REMOVED ---
  // Recalculation logic has moved to the recalculate_calculation_totals DB function
  // public async recalcCalculation(calcId: string): Promise<void> {
  //   this.logger.log(`Recalculating totals for calculation ID: ${calcId}`);
  //   const supabase = this.supabaseService.getClient();
  //   ...
  //   // Re-throw the exception to be handled by the caller or NestJS
  //   throw error;
  // }
}
