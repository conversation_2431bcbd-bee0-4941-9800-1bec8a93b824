import {
  IsNotEmpty,
  <PERSON>String,
  <PERSON><PERSON><PERSON>ber,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
} from 'class-validator';
import { Transform } from 'class-transformer';

export class TaxDetailItemDto {
  @IsOptional()
  @IsUUID()
  id?: string;

  @IsNotEmpty()
  @IsString()
  name: string;

  @IsNotEmpty()
  @IsNumber()
  @Min(0, { message: 'Tax rate must be at least 0%' })
  @Max(100, { message: 'Tax rate cannot exceed 100%' })
  @Transform(({ value }) => parseFloat(value))
  rate: number;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseFloat(value))
  amount?: number;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseFloat(value))
  applied_to_subtotal?: number;

  @IsOptional()
  @IsString()
  type?: string; // 'percentage' | 'fixed' (default: 'percentage')

  @IsOptional()
  @IsString()
  basis?: string; // 'subtotal' | 'total' (default: 'subtotal')
}

export class CreateTaxDto {
  @IsNotEmpty()
  @IsString()
  tax_name: string;

  @IsNotEmpty()
  @IsNumber()
  @Min(0, { message: 'Tax rate must be at least 0%' })
  @Max(100, { message: 'Tax rate cannot exceed 100%' })
  @Transform(({ value }) => parseFloat(value))
  tax_rate: number;

  @IsOptional()
  @IsString()
  type?: string; // 'percentage' | 'fixed'

  @IsOptional()
  @IsString()
  basis?: string; // 'subtotal' | 'total'
}

export class UpdateTaxDto {
  @IsOptional()
  @IsString()
  tax_name?: string;

  @IsOptional()
  @IsNumber()
  @Min(0, { message: 'Tax rate must be at least 0%' })
  @Max(100, { message: 'Tax rate cannot exceed 100%' })
  @Transform(({ value }) => parseFloat(value))
  tax_rate?: number;

  @IsOptional()
  @IsString()
  type?: string;

  @IsOptional()
  @IsString()
  basis?: string;
}
