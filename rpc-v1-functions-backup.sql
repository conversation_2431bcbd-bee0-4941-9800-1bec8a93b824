-- RPC V1 Functions Backup
-- Created during Phase 3 cleanup for potential restoration if needed
-- Date: Phase 3 Implementation

-- BACKUP: add_package_item_and_recalculate
CREATE OR REPLACE FUNCTION public.add_package_item_and_recalculate(p_calculation_id uuid, p_user_id uuid, p_package_id uuid, p_option_ids uuid[], p_currency_id uuid, p_quantity_override integer DEFAULT NULL::integer, p_duration_override integer DEFAULT NULL::integer, p_notes text DEFAULT NULL::text)
 RETURNS uuid
 LANGUAGE plpgsql
AS $function$
DECLARE
    v_calculation_owner_id uuid;
    v_attendees integer;
    v_pkg record;
    v_price record;
    v_options public.package_option_details_type[]; -- Use the defined type
    v_option_detail public.package_option_details_type; -- Use the defined type for loop variable
    v_determined_quantity integer;
    v_determined_duration integer;
    v_options_price_adjustment numeric(10, 2) := 0;
    v_options_cost_adjustment numeric(10, 2) := 0;
    v_option_summary_parts text[];
    v_option_summary text;
    v_calculated_line_total numeric(14, 2);
    v_calculated_line_cost numeric(14, 2);
    v_new_line_item_id uuid;
BEGIN
    -- 1. Verify Ownership & Get Attendees
    SELECT created_by, attendees INTO v_calculation_owner_id, v_attendees
    FROM public.calculation_history
    WHERE id = p_calculation_id AND is_deleted = false;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'Calculation not found: %', p_calculation_id;
    END IF;

    IF v_calculation_owner_id <> p_user_id THEN
        RAISE EXCEPTION 'User % does not own calculation %', p_user_id, p_calculation_id;
    END IF;

    -- 2. Fetch Package Details
    SELECT id, name, quantity_basis INTO v_pkg
    FROM public.packages
    WHERE id = p_package_id AND is_deleted = false;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'Package not found or deleted: %', p_package_id;
    END IF;

    -- 3. Fetch Package Price/Cost for Currency
    SELECT price AS unit_base_price, unit_base_cost INTO v_price
    FROM public.package_prices
    WHERE package_id = p_package_id AND currency_id = p_currency_id;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'Price not found for package % and currency %', p_package_id, p_currency_id;
    END IF;

    -- 4. Fetch Option Details for Currency
    IF array_length(p_option_ids, 1) > 0 THEN
      SELECT array_agg(o::public.package_option_details_type) INTO v_options -- Cast the selected row to the type
      FROM (
          SELECT
              id,
              option_name,
              price_adjustment,
              cost_adjustment
          FROM public.package_options
          WHERE applicable_package_id = p_package_id
            AND currency_id = p_currency_id
            AND id = ANY(p_option_ids)
      ) o;

      IF array_length(v_options, 1) <> array_length(p_option_ids, 1) THEN
          RAISE EXCEPTION 'One or more specified options not found or invalid for package % and currency %', p_package_id, p_currency_id;
      END IF;

       -- Calculate option adjustments and build summary
      FOREACH v_option_detail IN ARRAY v_options LOOP
          v_options_price_adjustment := v_options_price_adjustment + v_option_detail.price_adjustment;
          v_options_cost_adjustment := v_options_cost_adjustment + v_option_detail.cost_adjustment;
          v_option_summary_parts := array_append(v_option_summary_parts, v_option_detail.option_name);
      END LOOP;
      v_option_summary := array_to_string(v_option_summary_parts, ', ');

    END IF;


    -- 5. Determine Quantity & Duration
    CASE v_pkg.quantity_basis
        WHEN 'PER_ATTENDEE' THEN
            v_determined_quantity := COALESCE(p_quantity_override, v_attendees, 1);
            v_determined_duration := COALESCE(p_duration_override, 1);
        WHEN 'PER_DAY' THEN
            v_determined_quantity := COALESCE(p_quantity_override, 1);
            v_determined_duration := COALESCE(p_duration_override, 1);
        WHEN 'PER_ATTENDEE_PER_DAY' THEN
            v_determined_quantity := COALESCE(p_quantity_override, v_attendees, 1);
            v_determined_duration := COALESCE(p_duration_override, 1);
        -- WHEN 'PER_ITEM' or 'PER_EVENT' or 'PER_ITEM_PER_DAY'
        ELSE -- Default (includes PER_EVENT, PER_ITEM, PER_ITEM_PER_DAY which use overrides/defaults)
            v_determined_quantity := COALESCE(p_quantity_override, 1);
            v_determined_duration := COALESCE(p_duration_override, 1);
    END CASE;

    -- Ensure minimums
    v_determined_quantity := GREATEST(1, v_determined_quantity);
    v_determined_duration := GREATEST(1, v_determined_duration);

    -- 6. Calculate Line Totals - Standardized formula for all quantity basis types
    v_calculated_line_total := (v_price.unit_base_price + v_options_price_adjustment) * v_determined_quantity * v_determined_duration;
    v_calculated_line_cost := (v_price.unit_base_cost + v_options_cost_adjustment) * v_determined_quantity * v_determined_duration;

    -- 7. Insert Line Item
    INSERT INTO public.calculation_line_items (
        calculation_id,
        package_id,
        item_name_snapshot,
        option_summary_snapshot,
        item_quantity,
        item_quantity_basis,
        unit_base_price,
        options_total_adjustment,
        unit_base_cost_snapshot,
        options_total_cost_snapshot,
        calculated_line_total,
        calculated_line_cost,
        notes,
        currency_id, -- Store currency ID for consistency
        quantity_basis
    )
    VALUES (
        p_calculation_id,
        p_package_id,
        v_pkg.name,
        v_option_summary,
        v_determined_quantity,
        v_determined_duration,
        v_price.unit_base_price,
        v_options_price_adjustment,
        v_price.unit_base_cost,
        v_options_cost_adjustment,
        v_calculated_line_total,
        v_calculated_line_cost,
        p_notes,
        p_currency_id,
        v_pkg.quantity_basis
    )
    RETURNING id INTO v_new_line_item_id;

    -- 8. Insert Line Item Options
    IF array_length(v_options, 1) > 0 THEN
        INSERT INTO public.calculation_line_item_options (
            line_item_id,
            option_id,
            price_adjustment_snapshot,
            cost_adjustment_snapshot,
            currency_id -- Store currency ID
        )
        SELECT
            v_new_line_item_id,
            opt.id,
            opt.price_adjustment,
            opt.cost_adjustment,
            p_currency_id
        FROM unnest(v_options) AS opt;
    END IF;

    -- 9. Trigger Recalculation (MUST exist)
    -- Assuming recalculate_calculation_totals takes uuid and returns void/status
    PERFORM public.recalculate_calculation_totals(p_calculation_id);

    -- 10. Return new line item ID
    RETURN v_new_line_item_id;

END;
$function$

-- BACKUP: recalculate_calculation_totals
CREATE OR REPLACE FUNCTION public.recalculate_calculation_totals(p_calculation_id uuid)
 RETURNS void
 LANGUAGE plpgsql
AS $function$
DECLARE
    v_subtotal numeric(14, 2) := 0;
    v_total_cost numeric(14, 2) := 0;
    v_taxes jsonb;
    v_discount jsonb;
    v_tax_total numeric(14, 2) := 0;
    v_discount_amount numeric(14, 2) := 0;
    v_total numeric(14, 2);
    v_estimated_profit numeric(14, 2);
BEGIN
    -- CORRECTED: Calculate line item totals including package options
    -- Formula: (unit_base_price + options_total_adjustment) * item_quantity * item_quantity_basis
    UPDATE public.calculation_line_items
    SET calculated_line_total = (unit_base_price + COALESCE(options_total_adjustment, 0)) * item_quantity * item_quantity_basis,
        calculated_line_cost = (unit_base_cost_snapshot + COALESCE(options_total_cost_snapshot, 0)) * item_quantity * item_quantity_basis
    WHERE calculation_id = p_calculation_id;

    -- Calculate subtotal and total cost from package line items
    SELECT
        COALESCE(SUM(calculated_line_total), 0),
        COALESCE(SUM(calculated_line_cost), 0)
    INTO v_subtotal, v_total_cost
    FROM public.calculation_line_items
    WHERE calculation_id = p_calculation_id;

    -- Add custom items to subtotal and total cost
    SELECT
        v_subtotal + COALESCE(SUM(item_quantity * unit_price * item_quantity_basis), 0),
        v_total_cost + COALESCE(SUM(item_quantity * unit_cost * item_quantity_basis), 0)
    INTO v_subtotal, v_total_cost
    FROM public.calculation_custom_items
    WHERE calculation_id = p_calculation_id;

    -- Get taxes and discount from calculation
    SELECT taxes, discount
    INTO v_taxes, v_discount
    FROM public.calculation_history
    WHERE id = p_calculation_id;

    -- Calculate tax total (simplified)
    IF v_taxes IS NOT NULL AND jsonb_typeof(v_taxes) = 'array' THEN
        SELECT COALESCE(SUM((tax->>'rate')::numeric * v_subtotal / 100), 0)
        INTO v_tax_total
        FROM jsonb_array_elements(v_taxes) AS tax;
    END IF;

    -- Calculate discount amount (simplified)
    IF v_discount IS NOT NULL AND v_discount->>'value' IS NOT NULL THEN
        v_discount_amount := (v_discount->>'value')::numeric;
    END IF;

    -- Calculate final total and profit
    v_total := v_subtotal + v_tax_total - v_discount_amount;
    v_estimated_profit := v_total - v_total_cost;

    -- Update calculation_history with corrected totals
    UPDATE public.calculation_history
    SET
        subtotal = v_subtotal,
        total = v_total,
        total_cost = v_total_cost,
        estimated_profit = v_estimated_profit
    WHERE id = p_calculation_id;
END;
$function$

-- BACKUP: delete_line_item_and_recalculate
CREATE OR REPLACE FUNCTION public.delete_line_item_and_recalculate(p_calculation_id uuid, p_item_id uuid, p_user_id uuid, p_item_type text)
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    v_calculation_owner_id uuid;
    v_rows_affected integer;
BEGIN
    -- 1. Verify Ownership
    SELECT created_by INTO v_calculation_owner_id
    FROM public.calculation_history
    WHERE id = p_calculation_id AND is_deleted = false;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'Calculation not found: %', p_calculation_id;
    END IF;

    IF v_calculation_owner_id <> p_user_id THEN
        RAISE EXCEPTION 'User % does not own calculation %', p_user_id, p_calculation_id;
    END IF;

    -- 2. Delete item based on type
    IF lower(p_item_type) = 'package' THEN
        -- Delete associated options first (important for potential FKs, though CASCADE might handle it)
        DELETE FROM public.calculation_line_item_options
        WHERE line_item_id = p_item_id;

        -- Delete the package line item
        DELETE FROM public.calculation_line_items
        WHERE id = p_item_id AND calculation_id = p_calculation_id;
        GET DIAGNOSTICS v_rows_affected = ROW_COUNT;

        IF v_rows_affected = 0 THEN
             RAISE EXCEPTION 'Package line item not found or does not belong to calculation: Item ID %, Calc ID %', p_item_id, p_calculation_id;
        END IF;

    ELSIF lower(p_item_type) = 'custom' THEN
        -- Delete the custom line item
        DELETE FROM public.calculation_custom_items
        WHERE id = p_item_id AND calculation_id = p_calculation_id;
        GET DIAGNOSTICS v_rows_affected = ROW_COUNT;

        IF v_rows_affected = 0 THEN
             RAISE EXCEPTION 'Custom line item not found or does not belong to calculation: Item ID %, Calc ID %', p_item_id, p_calculation_id;
        END IF;

    ELSE
        RAISE EXCEPTION 'Invalid item type specified: %. Must be ''package'' or ''custom''.', p_item_type;
    END IF;

    -- 3. Trigger Recalculation
    PERFORM public.recalculate_calculation_totals(p_calculation_id);

END;
$function$
