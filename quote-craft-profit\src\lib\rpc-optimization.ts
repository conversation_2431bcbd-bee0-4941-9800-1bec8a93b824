/**
 * RPC Optimization Feature Flags and Utilities
 * 
 * This module provides feature flags and utilities for the RPC function optimization
 * implementation, allowing gradual migration from v1 to v2 RPC functions with
 * performance monitoring and fallback capabilities.
 */

interface RpcPerformanceMetrics {
  functionName: string;
  version: 'v1' | 'v2';
  executionTime: number;
  success: boolean;
  error?: string;
  timestamp: Date;
}

class RpcOptimizationManager {
  private performanceMetrics: RpcPerformanceMetrics[] = [];
  private maxMetricsHistory = 100;

  /**
   * Check if optimized RPC functions should be used
   */
  useOptimizedRPC(): boolean {
    return import.meta.env.VITE_USE_OPTIMIZED_RPC_FUNCTIONS === 'true';
  }

  /**
   * Check if performance monitoring is enabled
   */
  isPerformanceMonitoringEnabled(): boolean {
    return import.meta.env.VITE_RPC_PERFORMANCE_MONITORING === 'true';
  }

  /**
   * Record performance metrics for RPC function calls
   */
  recordPerformance(metrics: Omit<RpcPerformanceMetrics, 'timestamp'>): void {
    if (!this.isPerformanceMonitoringEnabled()) return;

    const fullMetrics: RpcPerformanceMetrics = {
      ...metrics,
      timestamp: new Date(),
    };

    this.performanceMetrics.push(fullMetrics);

    // Keep only the last N metrics to prevent memory leaks
    if (this.performanceMetrics.length > this.maxMetricsHistory) {
      this.performanceMetrics = this.performanceMetrics.slice(-this.maxMetricsHistory);
    }

    // Log performance data for debugging
    console.log(`[RPC Performance] ${metrics.functionName} (${metrics.version}): ${metrics.executionTime}ms`, {
      success: metrics.success,
      error: metrics.error,
    });
  }

  /**
   * Get performance statistics for analysis
   */
  getPerformanceStats(): {
    v1Stats: { avgTime: number; successRate: number; callCount: number };
    v2Stats: { avgTime: number; successRate: number; callCount: number };
    comparison: { speedImprovement: number; reliabilityChange: number };
  } {
    const v1Metrics = this.performanceMetrics.filter(m => m.version === 'v1');
    const v2Metrics = this.performanceMetrics.filter(m => m.version === 'v2');

    const calculateStats = (metrics: RpcPerformanceMetrics[]) => ({
      avgTime: metrics.length > 0 ? metrics.reduce((sum, m) => sum + m.executionTime, 0) / metrics.length : 0,
      successRate: metrics.length > 0 ? metrics.filter(m => m.success).length / metrics.length : 0,
      callCount: metrics.length,
    });

    const v1Stats = calculateStats(v1Metrics);
    const v2Stats = calculateStats(v2Metrics);

    return {
      v1Stats,
      v2Stats,
      comparison: {
        speedImprovement: v1Stats.avgTime > 0 ? ((v1Stats.avgTime - v2Stats.avgTime) / v1Stats.avgTime) * 100 : 0,
        reliabilityChange: (v2Stats.successRate - v1Stats.successRate) * 100,
      },
    };
  }

  /**
   * Clear performance metrics history
   */
  clearPerformanceHistory(): void {
    this.performanceMetrics = [];
  }

  /**
   * Export performance data for analysis
   */
  exportPerformanceData(): RpcPerformanceMetrics[] {
    return [...this.performanceMetrics];
  }
}

// Singleton instance
export const rpcOptimization = new RpcOptimizationManager();

/**
 * Decorator function to measure RPC function performance
 */
export function measureRpcPerformance<T extends any[], R>(
  functionName: string,
  version: 'v1' | 'v2'
) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: T): Promise<R> {
      const startTime = performance.now();
      let success = true;
      let error: string | undefined;

      try {
        const result = await originalMethod.apply(this, args);
        return result;
      } catch (err) {
        success = false;
        error = err instanceof Error ? err.message : 'Unknown error';
        throw err;
      } finally {
        const endTime = performance.now();
        const executionTime = endTime - startTime;

        rpcOptimization.recordPerformance({
          functionName,
          version,
          executionTime,
          success,
          error,
        });
      }
    };

    return descriptor;
  };
}

/**
 * Utility function to wrap RPC calls with performance monitoring
 */
export async function withRpcPerformanceMonitoring<T>(
  functionName: string,
  version: 'v1' | 'v2',
  rpcCall: () => Promise<T>
): Promise<T> {
  const startTime = performance.now();
  let success = true;
  let error: string | undefined;

  try {
    const result = await rpcCall();
    return result;
  } catch (err) {
    success = false;
    error = err instanceof Error ? err.message : 'Unknown error';
    throw err;
  } finally {
    const endTime = performance.now();
    const executionTime = endTime - startTime;

    rpcOptimization.recordPerformance({
      functionName,
      version,
      executionTime,
      success,
      error,
    });
  }
}

/**
 * Feature flag utilities for specific RPC functions
 */
export const rpcFeatureFlags = {
  useOptimizedRecalculation: () => rpcOptimization.useOptimizedRPC(),
  useOptimizedAddItem: () => rpcOptimization.useOptimizedRPC(),
  useOptimizedDeleteItem: () => rpcOptimization.useOptimizedRPC(),
  
  // Individual function overrides (for gradual rollout)
  forceV1Recalculation: () => import.meta.env.VITE_FORCE_V1_RECALCULATION === 'true',
  forceV1AddItem: () => import.meta.env.VITE_FORCE_V1_ADD_ITEM === 'true',
  forceV1DeleteItem: () => import.meta.env.VITE_FORCE_V1_DELETE_ITEM === 'true',
};

/**
 * Development utilities for testing and debugging
 */
export const rpcDevUtils = {
  /**
   * Log current feature flag status
   */
  logFeatureFlags: () => {
    console.group('[RPC Optimization] Feature Flags Status');
    console.log('Use Optimized RPC:', rpcOptimization.useOptimizedRPC());
    console.log('Performance Monitoring:', rpcOptimization.isPerformanceMonitoringEnabled());
    console.log('Force V1 Recalculation:', rpcFeatureFlags.forceV1Recalculation());
    console.log('Force V1 Add Item:', rpcFeatureFlags.forceV1AddItem());
    console.log('Force V1 Delete Item:', rpcFeatureFlags.forceV1DeleteItem());
    console.groupEnd();
  },

  /**
   * Log performance statistics
   */
  logPerformanceStats: () => {
    const stats = rpcOptimization.getPerformanceStats();
    console.group('[RPC Optimization] Performance Statistics');
    console.log('V1 Stats:', stats.v1Stats);
    console.log('V2 Stats:', stats.v2Stats);
    console.log('Speed Improvement:', `${stats.comparison.speedImprovement.toFixed(2)}%`);
    console.log('Reliability Change:', `${stats.comparison.reliabilityChange.toFixed(2)}%`);
    console.groupEnd();
  },

  /**
   * Reset all performance data
   */
  resetPerformanceData: () => {
    rpcOptimization.clearPerformanceHistory();
    console.log('[RPC Optimization] Performance data cleared');
  },
};

// Export the main manager instance
export { RpcOptimizationManager };
export type { RpcPerformanceMetrics };
