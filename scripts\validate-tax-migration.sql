-- Tax Migration Validation Script
-- Run this script to validate the tax migration was successful

-- ============================================================================
-- 1. VERIFY SCHEMA CHANGES
-- ============================================================================

-- Check that taxes column was removed from calculation_history
SELECT 
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'calculation_history' 
AND column_name = 'taxes';
-- Expected: No rows (column should be removed)

-- Check calculation_taxes table structure
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'calculation_taxes'
ORDER BY ordinal_position;
-- Expected: All required columns present

-- ============================================================================
-- 2. VERIFY DATA MIGRATION
-- ============================================================================

-- Check total number of tax records
SELECT 
    COUNT(*) as total_tax_records,
    COUNT(DISTINCT calculation_id) as calculations_with_taxes
FROM calculation_taxes;
-- Expected: Clean tax records, no duplicates

-- Verify tax calculations are correct
SELECT 
    ch.id,
    ch.name,
    ch.subtotal,
    ch.total,
    COUNT(ct.id) as tax_count,
    SUM(ct.tax_amount) as calculated_tax_total,
    ch.subtotal + COALESCE(SUM(ct.tax_amount), 0) as expected_total,
    CASE 
        WHEN ch.total = ch.subtotal + COALESCE(SUM(ct.tax_amount), 0) 
        THEN 'CORRECT' 
        ELSE 'INCORRECT' 
    END as validation_status
FROM calculation_history ch
LEFT JOIN calculation_taxes ct ON ch.id = ct.calculation_id
WHERE ch.id IN (
    SELECT DISTINCT calculation_id 
    FROM calculation_taxes
)
GROUP BY ch.id, ch.name, ch.subtotal, ch.total
ORDER BY ch.name;
-- Expected: All validation_status should be 'CORRECT'

-- ============================================================================
-- 3. VERIFY RPC FUNCTIONS
-- ============================================================================

-- Test the new recalculation function
SELECT 
    calc_id,
    result
FROM (
    SELECT 
        id as calc_id,
        recalculate_calculation_totals_v3(id) as result
    FROM calculation_history 
    WHERE id IN (
        SELECT DISTINCT calculation_id 
        FROM calculation_taxes 
        LIMIT 2
    )
) test_results;
-- Expected: Functions execute without errors

-- ============================================================================
-- 4. VERIFY INDEXES AND CONSTRAINTS
-- ============================================================================

-- Check indexes on calculation_taxes
SELECT 
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'calculation_taxes'
ORDER BY indexname;
-- Expected: Proper indexes for performance

-- Check foreign key constraints
SELECT
    tc.constraint_name,
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY'
AND tc.table_name = 'calculation_taxes';
-- Expected: Foreign key to calculation_history

-- ============================================================================
-- 5. VERIFY DATA CONSISTENCY
-- ============================================================================

-- Check for any orphaned tax records
SELECT 
    ct.id,
    ct.calculation_id,
    ct.tax_name
FROM calculation_taxes ct
LEFT JOIN calculation_history ch ON ct.calculation_id = ch.id
WHERE ch.id IS NULL;
-- Expected: No rows (no orphaned records)

-- Check for calculations with inconsistent totals
SELECT 
    ch.id,
    ch.name,
    ch.subtotal,
    ch.total,
    COALESCE(SUM(ct.tax_amount), 0) as tax_total,
    ch.subtotal + COALESCE(SUM(ct.tax_amount), 0) as calculated_total,
    ABS(ch.total - (ch.subtotal + COALESCE(SUM(ct.tax_amount), 0))) as difference
FROM calculation_history ch
LEFT JOIN calculation_taxes ct ON ch.id = ct.calculation_id
GROUP BY ch.id, ch.name, ch.subtotal, ch.total
HAVING ABS(ch.total - (ch.subtotal + COALESCE(SUM(ct.tax_amount), 0))) > 0.01
ORDER BY difference DESC;
-- Expected: No rows or minimal differences due to rounding

-- ============================================================================
-- 6. PERFORMANCE VALIDATION
-- ============================================================================

-- Test query performance for tax data retrieval
EXPLAIN ANALYZE
SELECT 
    ch.id,
    ch.name,
    ch.subtotal,
    ch.total,
    json_agg(
        json_build_object(
            'id', ct.id,
            'name', ct.tax_name,
            'rate', ct.tax_rate,
            'amount', ct.tax_amount
        )
    ) FILTER (WHERE ct.id IS NOT NULL) as taxes
FROM calculation_history ch
LEFT JOIN calculation_taxes ct ON ch.id = ct.calculation_id
WHERE ch.created_by = (SELECT id FROM auth.users LIMIT 1)
GROUP BY ch.id, ch.name, ch.subtotal, ch.total
LIMIT 10;
-- Expected: Fast execution with proper index usage

-- ============================================================================
-- 7. MIGRATION SUMMARY
-- ============================================================================

-- Generate migration summary report
SELECT 
    'MIGRATION SUMMARY' as report_section,
    json_build_object(
        'total_calculations', (SELECT COUNT(*) FROM calculation_history),
        'calculations_with_taxes', (SELECT COUNT(DISTINCT calculation_id) FROM calculation_taxes),
        'total_tax_records', (SELECT COUNT(*) FROM calculation_taxes),
        'average_taxes_per_calculation', (
            SELECT ROUND(AVG(tax_count), 2)
            FROM (
                SELECT COUNT(*) as tax_count
                FROM calculation_taxes
                GROUP BY calculation_id
            ) avg_calc
        ),
        'schema_migration_complete', (
            SELECT CASE 
                WHEN COUNT(*) = 0 THEN true 
                ELSE false 
            END
            FROM information_schema.columns 
            WHERE table_name = 'calculation_history' 
            AND column_name = 'taxes'
        )
    ) as summary_data;

-- Final validation message
SELECT 
    CASE 
        WHEN (
            SELECT COUNT(*) 
            FROM information_schema.columns 
            WHERE table_name = 'calculation_history' 
            AND column_name = 'taxes'
        ) = 0 
        AND (SELECT COUNT(*) FROM calculation_taxes) > 0
        THEN '✅ TAX MIGRATION COMPLETED SUCCESSFULLY'
        ELSE '❌ TAX MIGRATION VALIDATION FAILED'
    END as migration_status;
