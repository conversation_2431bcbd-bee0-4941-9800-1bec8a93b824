/**
 * Unified Line Item Service
 *
 * This service consolidates all line item functionality:
 * - Supabase direct operations (from calculationSupabaseService.ts)
 * - Line item CRUD operations (from supabaseLineItemService.ts)
 * - Backend API integration (from calculationApiService.ts)
 */

import { supabase } from "@/integrations/supabase/client";
import { getAuthenticatedApiClient } from "@/integrations/api/client";
import { API_ENDPOINTS } from "@/integrations/api/endpoints";
import {
  LineItem,
  LineItemInput,
  LineItemOption,
  QuantityBasisEnum,
} from "@/types/calculation";
import { showError, showLoading, updateToast } from "@/lib/notifications";

// ===== HELPER FUNCTIONS =====

/**
 * Helper function to calculate the total price of an item based on quantity_basis
 * @param item - The item to calculate the total price for
 * @returns The calculated total price
 */
const calculateItemTotal = (item: any): number => {
  const unitPrice = item.unit_price || 0;
  const quantity = item.item_quantity || 0;
  const itemQuantityBasis = item.item_quantity_basis || 1;
  const basis = item.quantity_basis || "PER_DAY";

  console.log(`[calculateItemTotal] Calculating for "${item.item_name}":`, {
    unitPrice,
    quantity,
    itemQuantityBasis,
    basis,
  });

  // ALL quantity basis types should multiply by unitPrice * quantity * itemQuantityBasis
  // This ensures consistent calculation logic across all types
  const total = unitPrice * quantity * itemQuantityBasis;

  console.log(
    `[calculateItemTotal] Result: ${unitPrice} × ${quantity} × ${itemQuantityBasis} = ${total}`
  );

  return total;
};

// ===== SUPABASE OPERATIONS =====

/**
 * Fetch all line items for a calculation directly from Supabase
 * @param calculationId - The calculation ID
 * @returns List of line items
 */
export const getCalculationLineItems = async (
  calculationId: string
): Promise<LineItem[]> => {
  try {
    console.log(
      `[Supabase] Fetching line items for calculation ID: ${calculationId}`
    );

    // First, fetch the package line items
    const { data: packageItems, error: packageItemsError } = await supabase
      .from("calculation_line_items")
      .select(
        `
        id,
        calculation_id,
        package_id,
        item_name_snapshot,
        item_quantity,
        item_quantity_basis,
        quantity_basis,
        unit_base_price,
        options_total_adjustment,
        calculated_line_total,
        notes,
        unit_base_cost_snapshot,
        options_total_cost_snapshot,
        calculated_line_cost,
        created_at,
        updated_at,
        calculation_line_item_options (
          id,
          option_id,
          price_adjustment_snapshot,
          package_options (
            option_name
          )
        )
      `
      )
      .eq("calculation_id", calculationId)
      .order("created_at", { ascending: true });

    if (packageItemsError) {
      console.error("Error fetching package line items:", packageItemsError);
      throw packageItemsError;
    }

    // Then, fetch the custom line items
    const { data: customItems, error: customItemsError } = await supabase
      .from("calculation_custom_items")
      .select(
        `
        id,
        calculation_id,
        item_name,
        description,
        item_quantity,
        item_quantity_basis,
        quantity_basis,
        unit_price,
        unit_cost,
        category_id,
        created_at,
        updated_at
      `
      )
      .eq("calculation_id", calculationId)
      .order("created_at", { ascending: true });

    if (customItemsError) {
      console.error("Error fetching custom line items:", customItemsError);
      throw customItemsError;
    }

    // Get package IDs to fetch their categories
    const packageIds = packageItems
      .filter((item) => item.package_id)
      .map((item) => item.package_id);

    // Fetch package categories if we have package items
    const packageCategoryMap = new Map<string, { id: string; name: string }>();
    if (packageIds.length > 0) {
      const { data: packages, error: packageError } = await supabase
        .from("packages")
        .select("id, category_id, categories(id, name)")
        .in("id", packageIds);

      if (!packageError && packages) {
        packages.forEach((pkg) => {
          if (pkg.category_id && pkg.categories) {
            packageCategoryMap.set(pkg.id, {
              id: pkg.categories.id,
              name: pkg.categories.name,
            });
          }
        });
      }
    }

    // Transform package items to LineItem format
    const transformedPackageItems: LineItem[] = packageItems.map((item) => {
      // Get category information for package items
      let categoryId = "";

      if (item.package_id && packageCategoryMap.has(item.package_id)) {
        const packageCategory = packageCategoryMap.get(item.package_id);
        categoryId = packageCategory.id;
      }

      // Convert quantity_basis string to enum
      let quantityBasisEnum: QuantityBasisEnum;
      if (item.quantity_basis && typeof item.quantity_basis === "string") {
        // Handle string values from database
        quantityBasisEnum =
          QuantityBasisEnum[
            item.quantity_basis as keyof typeof QuantityBasisEnum
          ] || QuantityBasisEnum.PER_DAY;
      } else {
        quantityBasisEnum = QuantityBasisEnum.PER_DAY;
      }

      return {
        id: item.id,
        calculation_id: item.calculation_id,
        package_id: item.package_id || undefined,
        name: item.item_name_snapshot,
        description: item.notes || undefined,
        quantity: item.item_quantity,
        item_quantity_basis: item.item_quantity_basis || 1,
        unit_price: item.unit_base_price,
        total_price: item.calculated_line_total,
        category_id: categoryId,
        is_custom: false,
        // Use the converted enum
        quantity_basis: quantityBasisEnum,
        selectedOptions:
          item.calculation_line_item_options?.map(
            (option) => option.option_id
          ) || [],
        created_at: item.created_at,
        updated_at: item.updated_at,
        createdAt: new Date(item.created_at),
        updatedAt: new Date(item.updated_at),
      };
    });

    // Transform custom items to LineItem format
    const transformedCustomItems: LineItem[] = customItems.map((item) => {
      // Debug: Log the raw item data
      console.log(`[Supabase] Raw custom item data for "${item.item_name}":`, {
        quantity_basis: item.quantity_basis,
        item_quantity: item.item_quantity,
        item_quantity_basis: item.item_quantity_basis,
        unit_price: item.unit_price,
      });

      // Convert quantity_basis string to enum
      let quantityBasisEnum: QuantityBasisEnum;
      if (item.quantity_basis && typeof item.quantity_basis === "string") {
        // Handle string values from database
        quantityBasisEnum =
          QuantityBasisEnum[
            item.quantity_basis as keyof typeof QuantityBasisEnum
          ] || QuantityBasisEnum.PER_DAY;
      } else {
        quantityBasisEnum = QuantityBasisEnum.PER_DAY;
      }

      console.log(
        `[Supabase] Converted quantity_basis for "${item.item_name}":`,
        {
          original: item.quantity_basis,
          converted: quantityBasisEnum,
        }
      );

      return {
        id: item.id,
        calculation_id: item.calculation_id,
        name: item.item_name,
        description: item.description || undefined,
        quantity: item.item_quantity, // Updated from quantity to item_quantity
        item_quantity_basis: item.item_quantity_basis || 1, // Added new field
        unit_price: item.unit_price,
        total_price: calculateItemTotal(item), // Use helper function to calculate total
        category_id: item.category_id || "",
        quantity_basis: quantityBasisEnum,
        is_custom: true,
        created_at: item.created_at,
        updated_at: item.updated_at,
        createdAt: new Date(item.created_at),
        updatedAt: new Date(item.updated_at),
      };
    });

    // Combine and return all items
    const allItems = [...transformedPackageItems, ...transformedCustomItems];
    console.log(`[Supabase] Fetched ${allItems.length} line items:`, allItems);

    // Don't show routine loading notifications - they're not important enough for users

    // Make sure we're returning an array even if empty
    return Array.isArray(allItems) ? allItems : [];
  } catch (error) {
    console.error(
      `[Supabase] Error fetching line items for calculation ID ${calculationId}:`,
      error
    );
    showError("Failed to load line items", {
      description:
        "There was an error loading the line items. Please try again.",
    });
    throw error;
  }
};

/**
 * Get line item options for a specific line item
 * @param lineItemId - The line item ID
 * @returns List of line item options
 */
export const getLineItemOptions = async (
  lineItemId: string
): Promise<LineItemOption[]> => {
  try {
    console.log(`[Supabase] Fetching options for line item ID: ${lineItemId}`);

    const { data, error } = await supabase
      .from("calculation_line_item_options")
      .select(
        `
        id,
        line_item_id,
        option_id,
        price_adjustment_snapshot,
        package_options (
          option_name
        )
      `
      )
      .eq("line_item_id", lineItemId);

    if (error) {
      console.error("Error fetching line item options:", error);
      throw error;
    }

    // Transform to LineItemOption format
    const options: LineItemOption[] = data.map((option) => ({
      id: option.id,
      line_item_id: option.line_item_id,
      package_option_id: option.option_id,
      name: option.package_options?.option_name || "Unknown Option",
      price_adjustment: option.price_adjustment_snapshot,
    }));

    console.log(`[Supabase] Fetched ${options.length} options for line item`);
    return options;
  } catch (error) {
    console.error(
      `[Supabase] Error fetching options for line item ID ${lineItemId}:`,
      error
    );
    showError("Failed to load line item options", {
      description:
        "There was an error loading the options for this line item. Please try again.",
    });
    throw error;
  }
};

/**
 * Add a line item to a calculation using Supabase RPC
 * @param calculationId - The calculation ID
 * @param lineItem - The line item data to add
 * @returns The created line item
 */
export const addLineItem = async (
  calculationId: string,
  lineItem: LineItemInput
): Promise<LineItem> => {
  try {
    console.log(
      `[Supabase] Adding line item to calculation ID ${calculationId}:`,
      lineItem
    );

    // Determine if this is a package or custom item
    if (!lineItem.is_custom && lineItem.package_id) {
      // First, fetch the calculation to get the currency ID
      const { data: calculationData, error: calculationError } = await supabase
        .from("calculation_history")
        .select("currency_id")
        .eq("id", calculationId)
        .single();

      if (calculationError) {
        console.error("Error fetching calculation currency:", calculationError);
        throw calculationError;
      }

      if (!calculationData?.currency_id) {
        throw new Error("Calculation currency ID not found");
      }

      // Use optimized v2 RPC function
      const { data: newLineItemId, error } = await supabase.rpc(
        "add_package_item_v2",
        {
          p_calculation_id: calculationId,
          p_user_id: (await supabase.auth.getUser()).data.user?.id,
          p_package_id: lineItem.package_id,
          p_option_ids: lineItem.selectedOptions || [],
          p_currency_id: calculationData.currency_id,
          p_quantity_override: lineItem.quantity,
          p_duration_override: lineItem.item_quantity_basis || 1,
          p_notes: lineItem.description || "",
        }
      );

      if (error) {
        console.error("Error adding package line item:", error);
        throw error;
      }

      // Fetch the newly created line item
      const newItem = await getLineItemById(calculationId, newLineItemId);

      // Don't show notification here - let the calling hook handle it to avoid duplicates

      return newItem;
    } else {
      // This is a custom line item
      // First, fetch the calculation to get the currency ID
      const { data: calcData, error: calcError } = await supabase
        .from("calculation_history")
        .select("currency_id")
        .eq("id", calculationId)
        .single();

      if (calcError) {
        console.error("Error fetching calculation currency:", calcError);
        throw calcError;
      }

      if (!calcData?.currency_id) {
        throw new Error("Calculation currency ID not found");
      }

      const currencyId = calcData.currency_id;

      const { data, error } = await supabase
        .from("calculation_custom_items")
        .insert({
          calculation_id: calculationId,
          item_name: lineItem.name,
          description: lineItem.description || null,
          item_quantity: lineItem.quantity,
          item_quantity_basis: lineItem.item_quantity_basis || 1,
          quantity_basis: lineItem.quantity_basis || "PER_DAY",
          unit_price: lineItem.unit_price || 0,
          unit_cost: 0, // Default to 0 if not provided
          category_id: lineItem.category_id || null,
          currency_id: currencyId, // Use the currency ID from the calculation
        })
        .select()
        .single();

      if (error) {
        console.error("Error adding custom line item:", error);
        throw error;
      }

      // Trigger recalculation
      await recalculateCalculationTotals(calculationId);

      // Transform to LineItem format
      const newItem: LineItem = {
        id: data.id,
        calculation_id: data.calculation_id,
        name: data.item_name,
        description: data.description || undefined,
        quantity: data.item_quantity,
        item_quantity_basis: data.item_quantity_basis || 1,
        unit_price: data.unit_price,
        total_price: calculateItemTotal(data),
        category_id: data.category_id || "",
        quantity_basis:
          data.quantity_basis && typeof data.quantity_basis === "string"
            ? QuantityBasisEnum[
                data.quantity_basis as keyof typeof QuantityBasisEnum
              ] || QuantityBasisEnum.PER_DAY
            : QuantityBasisEnum.PER_DAY,
        is_custom: true,
        created_at: data.created_at,
        updated_at: data.updated_at,
        createdAt: new Date(data.created_at),
        updatedAt: new Date(data.updated_at),
      };

      // Don't show notification here - let the calling hook handle it to avoid duplicates

      return newItem;
    }
  } catch (error) {
    console.error(
      `[Supabase] Error adding line item to calculation ID ${calculationId}:`,
      error
    );
    showError("Failed to add line item", {
      description:
        "There was an error adding the line item to the calculation. Please try again.",
    });
    throw error;
  }
};

/**
 * Helper function to get a line item by ID
 * @param calculationId - The calculation ID
 * @param lineItemId - The line item ID
 * @returns The line item
 */
export const getLineItemById = async (
  calculationId: string,
  lineItemId: string
): Promise<LineItem> => {
  // Get all line items and find the one we need
  const lineItems = await getCalculationLineItems(calculationId);
  const lineItem = lineItems.find((item) => item.id === lineItemId);

  if (!lineItem) {
    throw new Error(`Line item ${lineItemId} not found`);
  }

  return lineItem;
};

/**
 * Remove a line item from a calculation using optimized v2 RPC function
 * @param calculationId - The calculation ID
 * @param lineItemId - The line item ID
 * @param isCustom - Whether this is a custom item or package item
 */
export const removeLineItem = async (
  calculationId: string,
  lineItemId: string,
  isCustom: boolean = false
): Promise<void> => {
  try {
    console.log(
      `[Supabase] Removing line item ${lineItemId} from calculation ${calculationId}`
    );

    const { error } = await supabase.rpc("delete_line_item_v2", {
      p_calculation_id: calculationId,
      p_item_id: lineItemId,
      p_user_id: (await supabase.auth.getUser()).data.user?.id,
      p_item_type: isCustom ? "custom" : "package",
    });

    if (error) {
      console.error(`Error removing line item ${lineItemId}:`, error);
      throw error;
    }

    console.log(
      `[Supabase] Successfully removed line item ${lineItemId} with v2`
    );
  } catch (error) {
    console.error(`[Supabase] Error removing line item ${lineItemId}:`, error);
    showError("Failed to remove line item", {
      description:
        "There was an error removing the line item. Please try again.",
    });
    throw error;
  }
};

/**
 * Recalculate calculation totals using optimized v2 RPC function
 * @param calculationId - The calculation ID
 */
export const recalculateCalculationTotals = async (
  calculationId: string
): Promise<void> => {
  try {
    console.log(
      `Recalculating totals for calculation ${calculationId} with Supabase v2`
    );

    const { error } = await supabase.rpc("recalculate_calculation_totals_v2", {
      p_calculation_id: calculationId,
    });

    if (error) {
      console.error(
        `Error recalculating totals for calculation ${calculationId}:`,
        error
      );
      throw error;
    }

    console.log(
      `Successfully recalculated totals for calculation ${calculationId} with Supabase v2`
    );
  } catch (error) {
    console.error(
      `Error recalculating totals for calculation ${calculationId}:`,
      error
    );
    throw error;
  }
};

// ===== OPTIMIZED V2 FUNCTIONS (Direct Access) =====

/**
 * Add package item using optimized v2 RPC function directly
 * @param calculationId - The calculation ID
 * @param lineItem - The line item data to add
 * @returns The created line item ID
 */
export const addLineItemWithSupabase = async (
  calculationId: string,
  lineItem: LineItemInput
): Promise<string> => {
  if (!lineItem.package_id) {
    throw new Error("Package ID is required for v2 add function");
  }

  // Get calculation currency
  const { data: calculationData, error: calculationError } = await supabase
    .from("calculation_history")
    .select("currency_id")
    .eq("id", calculationId)
    .single();

  if (calculationError || !calculationData?.currency_id) {
    throw new Error("Calculation currency ID not found");
  }

  const { data: newLineItemId, error } = await supabase.rpc(
    "add_package_item_v2",
    {
      p_calculation_id: calculationId,
      p_user_id: (await supabase.auth.getUser()).data.user?.id,
      p_package_id: lineItem.package_id,
      p_option_ids: lineItem.selectedOptions || [],
      p_currency_id: calculationData.currency_id,
      p_quantity_override: lineItem.quantity,
      p_duration_override: lineItem.item_quantity_basis || 1,
      p_notes: lineItem.description || "",
    }
  );

  if (error) {
    console.error("Error adding package line item (v2):", error);
    throw error;
  }

  return newLineItemId;
};

/**
 * Recalculate totals using optimized v2 RPC function directly
 * @param calculationId - The calculation ID
 */
export const recalculateTotalsWithSupabase = async (
  calculationId: string
): Promise<void> => {
  const { error } = await supabase.rpc("recalculate_calculation_totals_v2", {
    p_calculation_id: calculationId,
  });

  if (error) {
    console.error("Error recalculating totals (v2):", error);
    throw error;
  }
};

/**
 * Get line item options from Supabase directly (for compatibility)
 * @param lineItemId - The line item ID
 * @returns List of line item options
 */
export const getLineItemOptionsFromSupabase = async (
  lineItemId: string
): Promise<LineItemOption[]> => {
  return await getLineItemOptions(lineItemId);
};

// ===== BACKEND API OPERATIONS =====

/**
 * Get line items from the backend API (alternative to Supabase)
 * @param calculationId - The calculation ID
 * @returns List of line items
 */
export const getCalculationLineItemsFromApi = async (
  calculationId: string
): Promise<LineItem[]> => {
  try {
    console.log(
      `[API] Fetching line items for calculation ID: ${calculationId}`
    );

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request
    const response = await authClient.get(
      API_ENDPOINTS.CALCULATIONS.LINE_ITEMS.GET_ALL(calculationId)
    );

    console.log(`[API] Fetched ${response.data.length} line items`);
    return response.data;
  } catch (error) {
    console.error(
      `[API] Error fetching line items for calculation ID ${calculationId}:`,
      error
    );
    showError("Failed to load line items", {
      description:
        "There was an error loading the line items. Please try again.",
    });
    throw error;
  }
};

/**
 * Add line item using the backend API (alternative to Supabase)
 * @param calculationId - The calculation ID
 * @param lineItem - The line item data to add
 * @returns The created line item
 */
export const addLineItemFromApi = async (
  calculationId: string,
  lineItem: LineItemInput
): Promise<LineItem> => {
  try {
    console.log(
      `[API] Adding line item to calculation ID ${calculationId}:`,
      lineItem
    );

    // Don't show loading toast for routine operations

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request - use ADD_CUSTOM for custom items or ADD_PACKAGE for package items
    const endpoint = lineItem.is_custom
      ? API_ENDPOINTS.CALCULATIONS.LINE_ITEMS.ADD_CUSTOM(calculationId)
      : API_ENDPOINTS.CALCULATIONS.LINE_ITEMS.ADD_PACKAGE(calculationId);

    const response = await authClient.post(endpoint, lineItem);

    // Don't show notification here - let the calling hook handle it to avoid duplicates

    console.log(`[API] Successfully added line item:`, response.data);
    return response.data;
  } catch (error) {
    console.error(
      `[API] Error adding line item to calculation ID ${calculationId}:`,
      error
    );
    showError("Failed to add line item", {
      description:
        "There was an error adding the line item to the calculation. Please try again.",
    });
    throw error;
  }
};

/**
 * Recalculate totals using the backend API (alternative to Supabase)
 * @param calculationId - The calculation ID
 */
export const recalculateCalculationTotalsFromApi = async (
  calculationId: string
): Promise<void> => {
  try {
    console.log(
      `[API] Triggering recalculation for calculation ID: ${calculationId}`
    );

    // Show loading toast
    const loadingToastId = showLoading("Recalculating totals...");

    // Get authenticated API client
    const authClient = await getAuthenticatedApiClient();

    // Make API request to recalculate totals
    await authClient.post(
      API_ENDPOINTS.CALCULATIONS.RECALCULATE(calculationId)
    );

    // Update toast to success
    updateToast(String(loadingToastId), "Totals recalculated", "success", {
      description: "Calculation totals have been updated successfully.",
    });

    console.log(
      `[API] Successfully recalculated totals for calculation ID: ${calculationId}`
    );
  } catch (error) {
    console.error(
      `[API] Error recalculating totals for calculation ID ${calculationId}:`,
      error
    );
    showError("Failed to recalculate totals", {
      description:
        "There was an error recalculating the totals. Please try again.",
    });
    throw error;
  }
};
