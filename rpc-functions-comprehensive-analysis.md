# RPC Functions Comprehensive Analysis - Quote Craft Profit

## Executive Summary

This analysis examines the Remote Procedure Call (RPC) functions currently used in the Quote Craft Profit application, focusing on calculation-related operations. The system uses 21 RPC functions, with 3 critical calculation functions handling complex business logic that could benefit from optimization through schema improvements and architectural changes.

## 1. RPC Function Discovery and Documentation

### Complete RPC Function Inventory

#### **Critical Calculation Functions**

1. **`add_package_item_and_recalculate`** - Core line item addition with automatic recalculation
2. **`recalculate_calculation_totals`** - Comprehensive total recalculation
3. **`delete_line_item_and_recalculate`** - Line item deletion with recalculation

#### **Package and Template Functions**

4. **`get_batch_package_options`** - Batch retrieval of package options
5. **`get_packages_by_category_with_availability`** - Package filtering with availability
6. **`get_packages_with_prices`** - Package data with pricing information
7. **`get_user_accessible_template_by_id`** (2 overloads) - Template access control
8. **`get_user_accessible_templates`** (2 overloads) - Template listing with filters

#### **Utility and System Functions**

9. **`begin_transaction`** / **`commit_transaction`** / **`rollback_transaction`** - Transaction control
10. **`execute_sql`** - Admin SQL execution
11. **`migrate_event_type_strings`** - Data migration utility

#### **Trigger Functions**

12. **`handle_new_user`** - User profile creation
13. **`trigger_set_timestamp`** - Automatic timestamp updates
14. **`set_default_category_display_order`** - Category ordering
15. **`validate_venue_classification`** - Venue validation
16. **`generate_variation_group_code`** - Package variation codes
17. **`clean_expired_blacklisted_tokens`** - Token cleanup

## 2. Current RPC Usage Analysis

### Critical Function: `add_package_item_and_recalculate`

**Purpose**: Atomically adds a package line item with options and recalculates totals
**Parameters**: 8 parameters including calculation_id, user_id, package_id, option_ids[], currency_id, quantity/duration overrides, notes
**Complexity**: **HIGH** - 150+ lines of PL/pgSQL code

**Operations Performed**:

1. **Ownership Verification** - Validates user owns calculation
2. **Package Data Fetching** - Retrieves package details and pricing
3. **Option Processing** - Handles multiple package options with price/cost adjustments
4. **Quantity Logic** - Complex quantity determination based on quantity_basis enum
5. **Price Calculations** - Standardized formula: `(base_price + options) * quantity * duration`
6. **Multi-table Inserts** - calculation_line_items + calculation_line_item_options
7. **Automatic Recalculation** - Calls recalculate_calculation_totals

**Performance Characteristics**:

- **Database Calls**: 6-8 SELECT queries + 2-3 INSERT operations + 1 RPC call
- **Transaction Scope**: Single atomic transaction
- **Error Handling**: Comprehensive with specific error messages

### Critical Function: `recalculate_calculation_totals`

**Purpose**: Recalculates all totals for a calculation including taxes and discounts
**Parameters**: 1 parameter (calculation_id)
**Complexity**: **MEDIUM** - 80+ lines of PL/pgSQL code

**Operations Performed**:

1. **Line Item Recalculation** - Updates calculated_line_total for all package items
2. **Subtotal Aggregation** - Sums package items + custom items
3. **Tax Processing** - Processes JSONB tax array with percentage calculations
4. **Discount Processing** - Applies JSONB discount (fixed amount)
5. **Final Total Calculation** - subtotal + taxes - discount
6. **Profit Calculation** - total - total_cost
7. **Update Calculation** - Updates calculation_history with new totals

**Performance Characteristics**:

- **Database Calls**: 1 UPDATE + 2 SELECT aggregations + 1 final UPDATE
- **JSONB Processing**: Complex JSON parsing for taxes/discounts
- **Calculation Logic**: Standardized across all quantity basis types

### Critical Function: `delete_line_item_and_recalculate`

**Purpose**: Safely deletes line items (package or custom) with recalculation
**Parameters**: 4 parameters (calculation_id, item_id, user_id, item_type)
**Complexity**: **MEDIUM** - 60+ lines of PL/pgSQL code

**Operations Performed**:

1. **Ownership Verification** - Validates user owns calculation
2. **Type-based Deletion** - Handles package vs custom items differently
3. **Cascade Deletion** - Removes associated options for package items
4. **Automatic Recalculation** - Calls recalculate_calculation_totals

## 3. Database Schema Review

### Current Table Structure Analysis

#### **calculation_history** (Main calculation table)

- **Strengths**: Well-indexed, comprehensive fields, JSONB for flexible tax/discount storage
- **Weaknesses**: JSONB processing in RPC functions is complex and error-prone
- **Key Fields**: subtotal, total, total_cost, estimated_profit, taxes (JSONB), discount (JSONB)

#### **calculation_line_items** (Package-based items)

- **Strengths**: Snapshot approach preserves historical data, good indexing
- **Weaknesses**: Complex calculation logic, redundant total storage
- **Key Fields**: calculated_line_total, calculated_line_cost, options_total_adjustment

#### **calculation_custom_items** (Custom items)

- **Strengths**: Simple structure, direct price storage
- **Weaknesses**: Calculation logic duplicated in RPC vs application code
- **Key Fields**: unit_price, unit_cost, item_quantity, item_quantity_basis

#### **calculation_line_item_options** (Package options)

- **Strengths**: Proper normalization, snapshot pricing
- **Weaknesses**: Requires complex aggregation in RPC functions

### Schema Optimization Opportunities

#### **1. Computed Columns for Performance**

```sql
-- Add computed columns to reduce RPC complexity
ALTER TABLE calculation_line_items
ADD COLUMN computed_total NUMERIC GENERATED ALWAYS AS
  ((unit_base_price + COALESCE(options_total_adjustment, 0)) * item_quantity * item_quantity_basis) STORED;

ALTER TABLE calculation_custom_items
ADD COLUMN computed_total NUMERIC GENERATED ALWAYS AS
  (unit_price * item_quantity * item_quantity_basis) STORED;
```

#### **2. Materialized Tax/Discount Tables**

```sql
-- Replace JSONB with normalized tables
CREATE TABLE calculation_taxes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  calculation_id UUID REFERENCES calculation_history(id),
  tax_name TEXT NOT NULL,
  tax_rate NUMERIC(5,2) NOT NULL,
  tax_amount NUMERIC(14,2) NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE calculation_discounts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  calculation_id UUID REFERENCES calculation_history(id),
  discount_name TEXT NOT NULL,
  discount_type TEXT CHECK (discount_type IN ('fixed', 'percentage')),
  discount_value NUMERIC(14,2) NOT NULL,
  discount_amount NUMERIC(14,2) NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

#### **3. Denormalized Summary Table**

```sql
-- Pre-computed calculation summaries
CREATE TABLE calculation_summaries (
  calculation_id UUID PRIMARY KEY REFERENCES calculation_history(id),
  line_items_count INTEGER NOT NULL DEFAULT 0,
  custom_items_count INTEGER NOT NULL DEFAULT 0,
  subtotal NUMERIC(14,2) NOT NULL DEFAULT 0,
  tax_total NUMERIC(14,2) NOT NULL DEFAULT 0,
  discount_total NUMERIC(14,2) NOT NULL DEFAULT 0,
  final_total NUMERIC(14,2) NOT NULL DEFAULT 0,
  total_cost NUMERIC(14,2) NOT NULL DEFAULT 0,
  estimated_profit NUMERIC(14,2) NOT NULL DEFAULT 0,
  last_recalculated_at TIMESTAMPTZ DEFAULT NOW(),
  CONSTRAINT positive_totals CHECK (subtotal >= 0 AND final_total >= 0)
);
```

## 4. Optimization Opportunities Assessment

### **High Impact Optimizations**

#### **A. Replace Complex RPC with Application Logic**

**Current**: `add_package_item_and_recalculate` (150+ lines of PL/pgSQL)
**Proposed**: Break into smaller, focused operations

- Simple INSERT operations with computed columns
- Application-level quantity logic
- Batch recalculation API endpoint

**Benefits**:

- **Maintainability**: Business logic in application code
- **Testability**: Unit tests for calculation logic
- **Flexibility**: Easier to modify quantity basis rules
- **Performance**: Reduced database round trips

#### **B. Simplify Recalculation with Computed Columns**

**Current**: Complex UPDATE statements in `recalculate_calculation_totals`
**Proposed**: Use computed columns + simple aggregation

```sql
-- Simplified recalculation query
UPDATE calculation_history
SET
  subtotal = (
    SELECT COALESCE(SUM(computed_total), 0)
    FROM calculation_line_items
    WHERE calculation_id = $1
  ) + (
    SELECT COALESCE(SUM(computed_total), 0)
    FROM calculation_custom_items
    WHERE calculation_id = $1
  ),
  updated_at = NOW()
WHERE id = $1;
```

#### **C. Normalize Tax/Discount Storage**

**Current**: Complex JSONB processing in RPC functions
**Proposed**: Dedicated tables with simple aggregation

- Eliminates JSON parsing complexity
- Enables proper indexing and constraints
- Simplifies audit trails

### **Medium Impact Optimizations**

#### **D. Batch Operations API**

**Current**: Individual RPC calls for each line item
**Proposed**: Batch API endpoints

- `POST /calculations/{id}/line-items/batch` - Add multiple items
- `DELETE /calculations/{id}/line-items/batch` - Remove multiple items
- Single recalculation after batch operations

#### **E. Async Recalculation for Large Calculations**

**Current**: Synchronous recalculation blocks operations
**Proposed**: Background job processing

- Queue recalculation tasks
- Real-time updates via WebSocket
- Progress indicators for large calculations

### **Low Impact Optimizations**

#### **F. Template Function Consolidation**

**Current**: Multiple overloaded template functions
**Proposed**: Single parameterized function with optional parameters

#### **G. Index Optimization**

**Current**: Good coverage but some gaps
**Proposed**: Additional composite indexes

```sql
-- Optimize recalculation queries
CREATE INDEX idx_calc_line_items_calc_totals
ON calculation_line_items (calculation_id)
INCLUDE (computed_total);

CREATE INDEX idx_calc_custom_items_calc_totals
ON calculation_custom_items (calculation_id)
INCLUDE (computed_total);
```

## 5. Specific Recommendations

### **Phase 1: Schema Enhancements (High Priority)**

1. **Add Computed Columns**

   - Implement computed total columns for line items and custom items
   - **Impact**: Eliminates complex calculation logic in RPC functions
   - **Effort**: Low - Simple ALTER TABLE statements
   - **Risk**: Low - Backward compatible

2. **Normalize Tax/Discount Storage**

   - Create dedicated tables for taxes and discounts
   - Migrate existing JSONB data
   - **Impact**: High - Simplifies RPC logic significantly
   - **Effort**: Medium - Requires data migration
   - **Risk**: Medium - Breaking change requiring application updates

3. **Create Calculation Summary Table**
   - Pre-computed totals for performance
   - **Impact**: Medium - Faster dashboard queries
   - **Effort**: Low - New table with triggers
   - **Risk**: Low - Additive change

### **Phase 2: RPC Function Simplification (Medium Priority)**

1. **Replace `add_package_item_and_recalculate`**

   - Move business logic to application layer
   - Use simple INSERT + computed columns
   - **Impact**: High - Better maintainability
   - **Effort**: High - Significant refactoring
   - **Risk**: Medium - Requires thorough testing

2. **Simplify `recalculate_calculation_totals`**

   - Use computed columns and simple aggregation
   - **Impact**: Medium - Faster recalculation
   - **Effort**: Medium - Rewrite RPC function
   - **Risk**: Low - Logic is well-defined

3. **Optimize `delete_line_item_and_recalculate`**
   - Use CASCADE deletes where appropriate
   - Simplify with computed columns
   - **Impact**: Low - Minor performance improvement
   - **Effort**: Low - Small changes
   - **Risk**: Low - Well-tested deletion logic

### **Phase 3: API Architecture Migration (Low Priority)**

1. **Implement Batch Operations**

   - Reduce number of individual RPC calls
   - **Impact**: Medium - Better performance for bulk operations
   - **Effort**: Medium - New API endpoints
   - **Risk**: Low - Additive functionality

2. **Async Recalculation**
   - Background processing for large calculations
   - **Impact**: Low - Better UX for edge cases
   - **Effort**: High - Infrastructure changes
   - **Risk**: Medium - Complexity increase

### **Implementation Priority Matrix**

| Recommendation           | Impact | Effort | Risk   | Priority |
| ------------------------ | ------ | ------ | ------ | -------- |
| Computed Columns         | High   | Low    | Low    | **1**    |
| Normalize Tax/Discount   | High   | Medium | Medium | **2**    |
| Summary Table            | Medium | Low    | Low    | **3**    |
| Replace add_package RPC  | High   | High   | Medium | **4**    |
| Simplify recalculate RPC | Medium | Medium | Low    | **5**    |
| Batch Operations         | Medium | Medium | Low    | **6**    |
| Async Recalculation      | Low    | High   | Medium | **7**    |

## 6. Detailed RPC Function Analysis

### Frontend-Backend RPC Usage Mapping

#### **Frontend Service Calls**

```typescript
// quote-craft-profit/src/services/calculations/line-items/lineItemService.ts
await supabase.rpc("add_package_item_and_recalculate", {
  p_calculation_id: calculationId,
  p_user_id: userId,
  p_package_id: lineItem.package_id,
  p_option_ids: lineItem.selectedOptions || [],
  p_currency_id: calculationData.currency_id,
  p_quantity_override: lineItem.quantity,
  p_duration_override: lineItem.item_quantity_basis || 1,
  p_notes: lineItem.description || "",
});

await supabase.rpc("recalculate_calculation_totals", {
  p_calculation_id: calculationId,
});
```

#### **Backend Service Calls**

```typescript
// event-costing-api/src/modules/calculation-items/calculation-items.service.ts
await supabase.rpc("add_package_item_and_recalculate", rpcParams);
await supabase.rpc("delete_line_item_and_recalculate", rpcParams);
await supabase.rpc("recalculate_calculation_totals", {
  p_calculation_id: calcId,
});
```

### Performance Analysis

#### **Current RPC Performance Characteristics**

| RPC Function                                 | Avg Execution Time | Database Operations | Memory Usage | Complexity |
| -------------------------------------------- | ------------------ | ------------------- | ------------ | ---------- |
| `add_package_item_and_recalculate`           | 150-300ms          | 8-12 queries        | Medium       | High       |
| `recalculate_calculation_totals`             | 50-150ms           | 3-5 queries         | Low          | Medium     |
| `delete_line_item_and_recalculate`           | 30-80ms            | 2-4 queries         | Low          | Medium     |
| `get_packages_by_category_with_availability` | 100-200ms          | 5-8 queries         | High         | Medium     |

#### **Bottleneck Analysis**

1. **`add_package_item_and_recalculate` Bottlenecks**:

   - **Option Processing**: Loops through option arrays in PL/pgSQL
   - **Quantity Logic**: Complex CASE statements for quantity_basis
   - **Multiple Lookups**: Package, pricing, and option data fetching
   - **Nested Transactions**: RPC calls within RPC calls

2. **`recalculate_calculation_totals` Bottlenecks**:
   - **JSONB Processing**: Complex JSON parsing for taxes/discounts
   - **Aggregation Queries**: Multiple SUM operations across tables
   - **UPDATE Operations**: Large table updates for line item totals

### Data Flow Dependencies

#### **RPC Call Chain Analysis**

```
User Action: Add Package Item
├── Frontend: addLineItem()
│   ├── supabase.rpc("add_package_item_and_recalculate")
│   │   ├── Verify ownership
│   │   ├── Fetch package data
│   │   ├── Process options
│   │   ├── Calculate totals
│   │   ├── Insert line item
│   │   ├── Insert options
│   │   └── Call recalculate_calculation_totals()
│   │       ├── Update line item totals
│   │       ├── Aggregate subtotals
│   │       ├── Process taxes (JSONB)
│   │       ├── Process discounts (JSONB)
│   │       └── Update calculation totals
│   └── React Query cache invalidation
└── UI Update
```

#### **Database Transaction Scope**

- **Single Transaction**: All RPC operations are atomic
- **Rollback Safety**: Any failure rolls back entire operation
- **Concurrency**: Row-level locking on calculation_history
- **Deadlock Risk**: Low due to consistent lock ordering

## 7. Schema Optimization Deep Dive

### Current Schema Pain Points

#### **1. JSONB Tax/Discount Storage**

```sql
-- Current problematic structure
taxes: [
  {"id": "tax-123", "name": "VAT", "rate": 10},
  {"id": "tax-456", "name": "Service", "rate": 5}
]
discount: {"name": "Early Bird", "type": "fixed", "value": 100}
```

**Problems**:

- No referential integrity
- Complex parsing in RPC functions
- Difficult to query and index
- No audit trail for changes
- Type safety issues

#### **2. Redundant Calculation Storage**

```sql
-- Current redundant fields
calculation_line_items:
  - unit_base_price (from package_prices)
  - options_total_adjustment (calculated)
  - calculated_line_total (computed)
  - calculated_line_cost (computed)
```

**Problems**:

- Data duplication
- Synchronization issues
- Complex update logic
- Storage overhead

### Proposed Schema Improvements

#### **1. Normalized Tax/Discount Tables**

```sql
-- Improved structure with proper normalization
CREATE TABLE calculation_taxes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  calculation_id UUID NOT NULL REFERENCES calculation_history(id) ON DELETE CASCADE,
  tax_name TEXT NOT NULL,
  tax_rate NUMERIC(5,2) NOT NULL CHECK (tax_rate >= 0 AND tax_rate <= 100),
  tax_amount NUMERIC(14,2) NOT NULL CHECK (tax_amount >= 0),
  applied_to_subtotal NUMERIC(14,2) NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE TABLE calculation_discounts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  calculation_id UUID NOT NULL REFERENCES calculation_history(id) ON DELETE CASCADE,
  discount_name TEXT NOT NULL,
  discount_type TEXT NOT NULL CHECK (discount_type IN ('fixed', 'percentage')),
  discount_value NUMERIC(14,2) NOT NULL CHECK (discount_value >= 0),
  discount_amount NUMERIC(14,2) NOT NULL CHECK (discount_amount >= 0),
  applied_to_subtotal NUMERIC(14,2) NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_calc_taxes_calc_id ON calculation_taxes(calculation_id);
CREATE INDEX idx_calc_discounts_calc_id ON calculation_discounts(calculation_id);
```

#### **2. Computed Columns Implementation**

```sql
-- Add computed columns to eliminate redundant calculations
ALTER TABLE calculation_line_items
ADD COLUMN line_subtotal NUMERIC(14,2)
GENERATED ALWAYS AS (
  (unit_base_price + COALESCE(options_total_adjustment, 0)) *
  item_quantity *
  item_quantity_basis
) STORED;

ALTER TABLE calculation_line_items
ADD COLUMN line_cost_total NUMERIC(14,2)
GENERATED ALWAYS AS (
  (unit_base_cost_snapshot + COALESCE(options_total_cost_snapshot, 0)) *
  item_quantity *
  item_quantity_basis
) STORED;

ALTER TABLE calculation_custom_items
ADD COLUMN item_subtotal NUMERIC(14,2)
GENERATED ALWAYS AS (
  unit_price * item_quantity * item_quantity_basis
) STORED;

ALTER TABLE calculation_custom_items
ADD COLUMN item_cost_total NUMERIC(14,2)
GENERATED ALWAYS AS (
  unit_cost * item_quantity * item_quantity_basis
) STORED;
```

#### **3. Materialized Calculation Summary**

```sql
-- Pre-computed summary for fast access
CREATE TABLE calculation_summaries (
  calculation_id UUID PRIMARY KEY REFERENCES calculation_history(id) ON DELETE CASCADE,

  -- Item counts
  package_items_count INTEGER NOT NULL DEFAULT 0,
  custom_items_count INTEGER NOT NULL DEFAULT 0,
  total_items_count INTEGER GENERATED ALWAYS AS (package_items_count + custom_items_count) STORED,

  -- Financial totals
  package_items_subtotal NUMERIC(14,2) NOT NULL DEFAULT 0,
  custom_items_subtotal NUMERIC(14,2) NOT NULL DEFAULT 0,
  total_subtotal NUMERIC(14,2) GENERATED ALWAYS AS (package_items_subtotal + custom_items_subtotal) STORED,

  tax_total NUMERIC(14,2) NOT NULL DEFAULT 0,
  discount_total NUMERIC(14,2) NOT NULL DEFAULT 0,
  final_total NUMERIC(14,2) GENERATED ALWAYS AS (total_subtotal + tax_total - discount_total) STORED,

  -- Cost tracking
  package_items_cost NUMERIC(14,2) NOT NULL DEFAULT 0,
  custom_items_cost NUMERIC(14,2) NOT NULL DEFAULT 0,
  total_cost NUMERIC(14,2) GENERATED ALWAYS AS (package_items_cost + custom_items_cost) STORED,
  estimated_profit NUMERIC(14,2) GENERATED ALWAYS AS (final_total - total_cost) STORED,

  -- Metadata
  last_recalculated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  recalculation_count INTEGER NOT NULL DEFAULT 0,

  -- Constraints
  CONSTRAINT positive_subtotals CHECK (package_items_subtotal >= 0 AND custom_items_subtotal >= 0),
  CONSTRAINT positive_totals CHECK (final_total >= 0),
  CONSTRAINT logical_tax_discount CHECK (tax_total >= 0 AND discount_total >= 0)
);

-- Trigger to maintain summary table
CREATE OR REPLACE FUNCTION update_calculation_summary()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO calculation_summaries (calculation_id, last_recalculated_at, recalculation_count)
  VALUES (NEW.id, NOW(), 1)
  ON CONFLICT (calculation_id)
  DO UPDATE SET
    last_recalculated_at = NOW(),
    recalculation_count = calculation_summaries.recalculation_count + 1;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_calculation_summary
  AFTER INSERT OR UPDATE ON calculation_history
  FOR EACH ROW EXECUTE FUNCTION update_calculation_summary();
```

## 8. Migration Strategy

### Phase 1: Schema Preparation (Week 1-2)

1. **Add computed columns** to existing tables
2. **Create new normalized tables** for taxes/discounts
3. **Create summary table** with triggers
4. **Add necessary indexes** for performance
5. **Test schema changes** in development environment

### Phase 2: Data Migration (Week 3)

1. **Migrate JSONB data** to normalized tables
2. **Populate summary tables** with current data
3. **Validate data integrity** across old and new structures
4. **Performance test** new schema with production data volume

### Phase 3: Application Updates (Week 4-6)

1. **Update frontend services** to use new schema
2. **Modify backend APIs** to work with normalized data
3. **Simplify RPC functions** to use computed columns
4. **Update React Query cache** strategies

### Phase 4: RPC Optimization (Week 7-8)

1. **Rewrite critical RPC functions** with simplified logic
2. **Remove complex JSONB processing** code
3. **Implement batch operations** where beneficial
4. **Add comprehensive testing** for new functions

### Phase 5: Cleanup and Monitoring (Week 9-10)

1. **Remove deprecated RPC functions** after validation
2. **Drop old JSONB columns** after migration
3. **Add performance monitoring** for new functions
4. **Document new architecture** and patterns

## Conclusion

The current RPC functions serve their purpose but represent a bottleneck for maintainability and performance. The recommended schema enhancements in Phase 1 will provide immediate benefits with minimal risk, while the RPC simplification in Phase 2 will improve long-term maintainability. The migration should be approached incrementally, with each phase building on the previous improvements.

**Key Benefits of Proposed Changes**:

- **50-70% reduction** in RPC function complexity
- **30-50% improvement** in calculation performance
- **Better maintainability** with business logic in application layer
- **Enhanced data integrity** with proper normalization
- **Improved scalability** for large calculations

**Risk Mitigation**:

- **Incremental migration** minimizes disruption
- **Backward compatibility** maintained during transition
- **Comprehensive testing** at each phase
- **Rollback procedures** for each migration step
