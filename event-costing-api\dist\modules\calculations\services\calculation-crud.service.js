"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var CalculationCrudService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CalculationCrudService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../../core/supabase/supabase.service");
let CalculationCrudService = CalculationCrudService_1 = class CalculationCrudService {
    supabaseService;
    logger = new common_1.Logger(CalculationCrudService_1.name);
    constructor(supabaseService) {
        this.supabaseService = supabaseService;
    }
    async createCalculation(createCalculationDto, user) {
        this.logger.log(`Creating calculation '${createCalculationDto.name}' for user ID: ${user.id}`);
        const supabase = this.supabaseService.getClient();
        const calculationData = {
            name: createCalculationDto.name,
            currency_id: createCalculationDto.currency_id,
            city_id: createCalculationDto.city_id ?? null,
            event_start_date: createCalculationDto.event_start_date ?? null,
            event_end_date: createCalculationDto.event_end_date ?? null,
            attendees: createCalculationDto.attendees ?? null,
            event_type_id: createCalculationDto.event_type_id ?? null,
            client_id: createCalculationDto.client_id ?? null,
            event_id: createCalculationDto.event_id ?? null,
            notes: createCalculationDto.notes ?? null,
            version_notes: createCalculationDto.version_notes ?? null,
            created_by: user.id,
            status: 'draft',
            subtotal: 0,
            discount: null,
            total: 0,
            total_cost: 0,
            estimated_profit: 0,
        };
        const { data, error } = await supabase
            .from('calculation_history')
            .insert(calculationData)
            .select('id')
            .single();
        if (error) {
            this.logger.error(`Failed to create calculation for user ${user.id}: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Could not create calculation.' +
                (error.details ? ` (${error.details})` : ''));
        }
        if (!data?.id) {
            this.logger.error(`Calculation created for user ${user.id} but ID was not returned.`);
            throw new common_1.InternalServerErrorException('Could not retrieve ID after calculation creation.');
        }
        this.logger.log(`Calculation created successfully with ID: ${data.id}`);
        return data.id;
    }
    async findUserCalculations(user, queryParams) {
        this.logger.log(`User ${user.email} listing calculations with query: ${JSON.stringify(queryParams)}`);
        const { limit = 20, offset = 0, sortBy = 'created_at', sortOrder = 'desc', status, clientId, } = queryParams;
        let query = this.supabaseService
            .getClient()
            .from('calculation_history')
            .select(`
        id, name, status, event_start_date, event_end_date,
        total, total_cost, estimated_profit, currency_id, created_at, updated_at,
        client_id, event_id,
        clients ( id, client_name ),
        events ( id, event_name ),
        currencies ( id, code )
      `, { count: 'exact' })
            .eq('created_by', user.id)
            .eq('is_deleted', false)
            .order(sortBy, { ascending: sortOrder === 'asc' })
            .range(offset, offset + limit - 1);
        if (status) {
            query = query.eq('status', status);
        }
        if (clientId) {
            query = query.eq('client_id', clientId);
        }
        const { data, error, count } = await query.returns();
        if (error) {
            this.logger.error(`Error fetching calculations for user ${user.email}: ${error.message}`);
            throw new common_1.InternalServerErrorException('Failed to retrieve calculations.');
        }
        const calculationSummaries = (data ?? []).map(calc => ({
            id: calc.id,
            name: calc.name,
            status: calc.status,
            event_start_date: calc.event_start_date,
            event_end_date: calc.event_end_date,
            total: calc.total ?? 0,
            total_cost: calc.total_cost ?? 0,
            estimated_profit: calc.estimated_profit ?? 0,
            currency_id: calc.currency_id,
            created_at: calc.created_at,
            updated_at: calc.updated_at,
            client_id: calc.client_id ?? undefined,
            event_id: calc.event_id ?? undefined,
        }));
        return {
            data: calculationSummaries,
            count: count ?? 0,
            limit: limit,
            offset: offset,
        };
    }
    async findCalculationRawById(id, user) {
        this.logger.log(`Finding calculation detail for ID: ${id} by user ${user.id}`);
        const supabase = this.supabaseService.getClient();
        const selectStatement = `
      id, name, currency_id, city_id, client_id, event_id, event_start_date, event_end_date, attendees, event_type_id, notes, version_notes, status, subtotal, discount, total, total_cost, estimated_profit, created_by, created_at, updated_at,
      currency:currencies ( id, code ),
      cities ( id, name ),
      clients ( id, client_name, contact_person, email ),
      events ( id, event_name ),
      calculation_taxes ( id, tax_name, tax_rate, tax_amount, applied_to_subtotal ),
      calculation_discounts ( id, discount_name, discount_type, discount_value, discount_amount, applied_to_subtotal ),
      calculation_line_items (
        id, package_id, item_name_snapshot, option_summary_snapshot, item_quantity, item_quantity_basis, unit_base_price, options_total_adjustment, calculated_line_total, notes, unit_base_cost_snapshot, options_total_cost_snapshot, calculated_line_cost,
        calculation_line_item_options (
          option_id,
          price_adjustment_snapshot,
          package_options ( option_name )
        )
      ),
      calculation_custom_items ( id, item_name, description, item_quantity, unit_price, unit_cost )
    `;
        const { data: raw, error } = await supabase
            .from('calculation_history')
            .select(selectStatement)
            .eq('id', id)
            .eq('is_deleted', false)
            .maybeSingle();
        if (error) {
            this.logger.error(`Error fetching calculation ID ${id} for user ${user.id}: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Could not retrieve calculation details.' +
                (error.details ? ` (${error.details})` : ''));
        }
        if (!raw) {
            throw new common_1.NotFoundException(`Calculation with ID ${id} not found.`);
        }
        if (raw.created_by !== user.id) {
            this.logger.warn(`User ${user.id} attempt to access calc ${id} owned by ${raw.created_by}`);
            throw new common_1.NotFoundException(`Calculation with ID ${id} not found or not accessible.`);
        }
        return raw;
    }
    async updateCalculationData(id, updateData, user) {
        this.logger.log(`User ${user.id} updating calculation ${id}`);
        const supabase = this.supabaseService.getClient();
        const { taxes, discount, ...calculationData } = updateData;
        const { error: updateError } = await supabase
            .from('calculation_history')
            .update(calculationData)
            .eq('id', id)
            .select('id')
            .single();
        if (updateError) {
            this.logger.error(`Failed to update calculation ${id}: ${updateError.message}`, updateError.stack);
            throw new common_1.InternalServerErrorException('Could not update calculation.' +
                (updateError.details ? ` (${updateError.details})` : ''));
        }
        if (taxes !== undefined) {
            await this.updateCalculationTaxes(id, taxes);
        }
        if (discount !== undefined) {
            await this.updateCalculationDiscount(id, discount);
        }
    }
    async deleteCalculation(id, user) {
        this.logger.log(`User ${user.id} attempting to soft delete calculation ${id}`);
        const supabase = this.supabaseService.getClient();
        const { error: deleteError } = await supabase
            .from('calculation_history')
            .update({ is_deleted: true, deleted_at: new Date().toISOString() })
            .match({ id: id, created_by: user.id });
        if (deleteError) {
            this.logger.error(`Failed to soft delete calculation ${id}: ${deleteError.message}`, deleteError.stack);
            throw new common_1.InternalServerErrorException('Failed to delete calculation.' +
                (deleteError.details ? ` (${deleteError.details})` : ''));
        }
        this.logger.log(`Calculation ${id} soft deleted successfully by user ${user.id}`);
    }
    async updateCalculationTaxes(calculationId, taxes) {
        const supabase = this.supabaseService.getClient();
        const { error: deleteError } = await supabase
            .from('calculation_taxes')
            .delete()
            .eq('calculation_id', calculationId);
        if (deleteError) {
            this.logger.error(`Failed to delete existing taxes for calculation ${calculationId}: ${deleteError.message}`);
            throw new common_1.InternalServerErrorException('Failed to update taxes');
        }
        if (taxes && Array.isArray(taxes) && taxes.length > 0) {
            const { data: calculation, error: calcError } = await supabase
                .from('calculation_history')
                .select('subtotal')
                .eq('id', calculationId)
                .single();
            if (calcError || !calculation) {
                this.logger.error(`Failed to get calculation subtotal for tax calculation: ${calcError?.message}`);
                throw new common_1.InternalServerErrorException('Failed to calculate taxes');
            }
            const subtotal = parseFloat(calculation.subtotal.toString()) || 0;
            const taxRecords = taxes.map(tax => {
                const taxRate = parseFloat(tax.rate.toString()) || 0;
                const taxAmount = (subtotal * taxRate) / 100;
                return {
                    calculation_id: calculationId,
                    tax_name: tax.name,
                    tax_rate: taxRate,
                    tax_amount: taxAmount,
                    applied_to_subtotal: subtotal,
                };
            });
            const { error: insertError } = await supabase
                .from('calculation_taxes')
                .insert(taxRecords);
            if (insertError) {
                this.logger.error(`Failed to insert new taxes for calculation ${calculationId}: ${insertError.message}`);
                throw new common_1.InternalServerErrorException('Failed to save taxes');
            }
        }
    }
    async updateCalculationDiscount(calculationId, discount) {
        const supabase = this.supabaseService.getClient();
        const { error: deleteError } = await supabase
            .from('calculation_discounts')
            .delete()
            .eq('calculation_id', calculationId);
        if (deleteError) {
            this.logger.error(`Failed to delete existing discounts for calculation ${calculationId}: ${deleteError.message}`);
            throw new common_1.InternalServerErrorException('Failed to update discount');
        }
        if (discount && discount.name) {
            const { data: calculation, error: calcError } = await supabase
                .from('calculation_history')
                .select('subtotal')
                .eq('id', calculationId)
                .single();
            if (calcError || !calculation) {
                this.logger.error(`Failed to get calculation subtotal for discount calculation: ${calcError?.message}`);
                throw new common_1.InternalServerErrorException('Failed to calculate discount');
            }
            const subtotal = parseFloat(calculation.subtotal.toString()) || 0;
            const discountValue = parseFloat(discount.amount?.toString() || discount.value?.toString() || '0') || 0;
            let discountAmount = 0;
            if (discount.type === 'percentage') {
                discountAmount = (subtotal * discountValue) / 100;
            }
            else {
                discountAmount = discountValue;
            }
            const discountRecord = {
                calculation_id: calculationId,
                discount_name: discount.name,
                discount_type: discount.type || 'fixed',
                discount_value: discountValue,
                discount_amount: discountAmount,
                applied_to_subtotal: subtotal,
            };
            const { error: insertError } = await supabase
                .from('calculation_discounts')
                .insert(discountRecord);
            if (insertError) {
                this.logger.error(`Failed to insert new discount for calculation ${calculationId}: ${insertError.message}`);
                throw new common_1.InternalServerErrorException('Failed to save discount');
            }
        }
        const { error: updateError } = await supabase
            .from('calculation_history')
            .update({ discount })
            .eq('id', calculationId);
        if (updateError) {
            this.logger.error(`Failed to update legacy discount field for calculation ${calculationId}: ${updateError.message}`);
        }
    }
    async triggerRecalculation(id) {
        const supabase = this.supabaseService.getClient();
        try {
            this.logger.log(`Attempting to trigger recalculation after update for calculation ID: ${id}`);
            const { error: rpcError } = await supabase.rpc('recalculate_calculation_totals', { p_calculation_id: id });
            if (rpcError) {
                this.logger.error(`Error calling recalculate_calculation_totals RPC for calculation ${id}: ${rpcError.message}`, rpcError.stack);
            }
            else {
                this.logger.log(`Successfully triggered recalculation after update for calculation ID: ${id}`);
            }
        }
        catch (recalcError) {
            const errorMessage = recalcError instanceof Error
                ? recalcError.message
                : String(recalcError);
            const errorStack = recalcError instanceof Error ? recalcError.stack : undefined;
            this.logger.error(`Recalculation RPC call failed unexpectedly after update for calculation ID: ${id} - ${errorMessage}`, errorStack);
        }
    }
};
exports.CalculationCrudService = CalculationCrudService;
exports.CalculationCrudService = CalculationCrudService = CalculationCrudService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService])
], CalculationCrudService);
//# sourceMappingURL=calculation-crud.service.js.map