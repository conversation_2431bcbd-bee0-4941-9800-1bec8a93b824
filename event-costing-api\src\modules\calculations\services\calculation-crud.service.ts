import {
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { SupabaseService } from '../../../core/supabase/supabase.service';
import { User } from '@supabase/supabase-js';
import { CreateCalculationDto } from '../dto/create-calculation.dto';
import { ListCalculationsDto } from '../dto/list-calculations.dto';
import { CalculationDetailDto } from '../dto/calculation-detail.dto';
import { CalculationSummaryDto } from '../dto/paginated-calculations.dto';
import { UpdateCalculationDto } from '../dto/update-calculation.dto';
import { PaginatedResponseDto } from '../../../shared/dtos/paginated-response.dto';
import {
  CalculationHistoryRaw,
  CalculationSummaryRaw,
} from '../interfaces/calculation-internal.interfaces';

/**
 * Service responsible for CRUD operations on calculations
 * Extracted from the main CalculationsService for better separation of concerns
 */
@Injectable()
export class CalculationCrudService {
  private readonly logger = new Logger(CalculationCrudService.name);

  constructor(private readonly supabaseService: SupabaseService) {}

  /**
   * Create a new calculation
   */
  async createCalculation(
    createCalculationDto: CreateCalculationDto,
    user: User,
  ): Promise<string> {
    this.logger.log(
      `Creating calculation '${createCalculationDto.name}' for user ID: ${user.id}`,
    );
    const supabase = this.supabaseService.getClient();

    const calculationData = {
      name: createCalculationDto.name,
      currency_id: createCalculationDto.currency_id,
      city_id: createCalculationDto.city_id ?? null,
      event_start_date: createCalculationDto.event_start_date ?? null,
      event_end_date: createCalculationDto.event_end_date ?? null,
      attendees: createCalculationDto.attendees ?? null,
      event_type_id: createCalculationDto.event_type_id ?? null, // Updated to use event_type_id
      client_id: createCalculationDto.client_id ?? null,
      event_id: createCalculationDto.event_id ?? null,
      notes: createCalculationDto.notes ?? null,
      version_notes: createCalculationDto.version_notes ?? null,
      created_by: user.id,
      status: 'draft',
      subtotal: 0,
      discount: null,
      total: 0,
      total_cost: 0,
      estimated_profit: 0,
    };

    // Insert calculation data
    const { data, error } = await supabase
      .from('calculation_history')
      .insert(calculationData)
      .select('id')
      .single();

    if (error) {
      this.logger.error(
        `Failed to create calculation for user ${user.id}: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException(
        'Could not create calculation.' +
          (error.details ? ` (${error.details})` : ''),
      );
    }

    if (!data?.id) {
      this.logger.error(
        `Calculation created for user ${user.id} but ID was not returned.`,
      );
      throw new InternalServerErrorException(
        'Could not retrieve ID after calculation creation.',
      );
    }

    this.logger.log(`Calculation created successfully with ID: ${data.id}`);
    return data.id as string;
  }

  /**
   * Find user calculations with pagination and filtering
   */
  async findUserCalculations(
    user: User,
    queryParams: ListCalculationsDto,
  ): Promise<PaginatedResponseDto<CalculationSummaryDto>> {
    this.logger.log(
      `User ${user.email} listing calculations with query: ${JSON.stringify(
        queryParams,
      )}`,
    );

    const {
      limit = 20,
      offset = 0,
      sortBy = 'created_at',
      sortOrder = 'desc',
      status,
      clientId,
    } = queryParams;

    let query = this.supabaseService
      .getClient()
      .from('calculation_history')
      .select(
        `
        id, name, status, event_start_date, event_end_date,
        total, total_cost, estimated_profit, currency_id, created_at, updated_at,
        client_id, event_id,
        clients ( id, client_name ),
        events ( id, event_name ),
        currencies ( id, code )
      `,
        { count: 'exact' }, // Request total count
      )
      .eq('created_by', user.id)
      .eq('is_deleted', false)
      .order(sortBy, { ascending: sortOrder === 'asc' })
      .range(offset, offset + limit - 1);

    if (status) {
      query = query.eq('status', status);
    }
    if (clientId) {
      query = query.eq('client_id', clientId);
    }

    const { data, error, count } =
      await query.returns<CalculationSummaryRaw[]>();

    if (error) {
      this.logger.error(
        `Error fetching calculations for user ${user.email}: ${error.message}`,
      );
      throw new InternalServerErrorException(
        'Failed to retrieve calculations.',
      );
    }

    const calculationSummaries: CalculationSummaryDto[] = (data ?? []).map(
      calc => ({
        id: calc.id,
        name: calc.name,
        status: calc.status,
        event_start_date: calc.event_start_date,
        event_end_date: calc.event_end_date,
        total: calc.total ?? 0,
        total_cost: calc.total_cost ?? 0,
        estimated_profit: calc.estimated_profit ?? 0,
        currency_id: calc.currency_id,
        created_at: calc.created_at,
        updated_at: calc.updated_at,
        client_id: calc.client_id ?? undefined,
        event_id: calc.event_id ?? undefined,
      }),
    );

    return {
      data: calculationSummaries,
      count: count ?? 0,
      limit: limit,
      offset: offset,
    };
  }

  /**
   * Find calculation by ID with basic data (without transformation)
   */
  async findCalculationRawById(
    id: string,
    user: User,
  ): Promise<CalculationHistoryRaw> {
    this.logger.log(
      `Finding calculation detail for ID: ${id} by user ${user.id}`,
    );
    const supabase = this.supabaseService.getClient();

    const selectStatement = `
      id, name, currency_id, city_id, client_id, event_id, event_start_date, event_end_date, attendees, event_type_id, notes, version_notes, status, subtotal, discount, total, total_cost, estimated_profit, created_by, created_at, updated_at,
      currency:currencies ( id, code ),
      cities ( id, name ),
      clients ( id, client_name, contact_person, email ),
      events ( id, event_name ),
      calculation_taxes ( id, tax_name, tax_rate, tax_amount, applied_to_subtotal ),
      calculation_line_items (
        id, package_id, item_name_snapshot, option_summary_snapshot, item_quantity, item_quantity_basis, unit_base_price, options_total_adjustment, calculated_line_total, notes, unit_base_cost_snapshot, options_total_cost_snapshot, calculated_line_cost,
        calculation_line_item_options (
          option_id,
          price_adjustment_snapshot,
          package_options ( option_name )
        )
      ),
      calculation_custom_items ( id, item_name, description, item_quantity, unit_price, unit_cost )
    `;

    const { data: raw, error } = await supabase
      .from('calculation_history')
      .select(selectStatement)
      .eq('id', id)
      .eq('is_deleted', false)
      .maybeSingle<CalculationHistoryRaw>();

    if (error) {
      this.logger.error(
        `Error fetching calculation ID ${id} for user ${user.id}: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException(
        'Could not retrieve calculation details.' +
          (error.details ? ` (${error.details})` : ''),
      );
    }

    if (!raw) {
      throw new NotFoundException(`Calculation with ID ${id} not found.`);
    }
    if (raw.created_by !== user.id) {
      this.logger.warn(
        `User ${user.id} attempt to access calc ${id} owned by ${raw.created_by}`,
      );
      throw new NotFoundException(
        `Calculation with ID ${id} not found or not accessible.`,
      );
    }

    return raw;
  }

  /**
   * Update calculation basic data (without venues)
   */
  async updateCalculationData(
    id: string,
    updateData: Omit<UpdateCalculationDto, 'venue_ids'>,
    user: User,
  ): Promise<void> {
    this.logger.log(`User ${user.id} updating calculation ${id}`);

    const supabase = this.supabaseService.getClient();

    // Extract taxes and discount from updateData
    const { taxes, discount, ...calculationData } = updateData;

    // Update calculation data (excluding taxes which are handled separately)
    const { error: updateError } = await supabase
      .from('calculation_history')
      .update(calculationData)
      .eq('id', id)
      .select('id')
      .single();

    if (updateError) {
      this.logger.error(
        `Failed to update calculation ${id}: ${updateError.message}`,
        updateError.stack,
      );
      throw new InternalServerErrorException(
        'Could not update calculation.' +
          (updateError.details ? ` (${updateError.details})` : ''),
      );
    }

    // Handle taxes separately if provided
    if (taxes !== undefined) {
      await this.updateCalculationTaxes(id, taxes);
    }

    // Handle discount separately if provided
    if (discount !== undefined) {
      await this.updateCalculationDiscount(id, discount);
    }
  }

  /**
   * Soft delete a calculation
   */
  async deleteCalculation(id: string, user: User): Promise<void> {
    this.logger.log(
      `User ${user.id} attempting to soft delete calculation ${id}`,
    );

    const supabase = this.supabaseService.getClient();

    const { error: deleteError } = await supabase
      .from('calculation_history')
      .update({ is_deleted: true, deleted_at: new Date().toISOString() })
      .match({ id: id, created_by: user.id });

    if (deleteError) {
      this.logger.error(
        `Failed to soft delete calculation ${id}: ${deleteError.message}`,
        deleteError.stack,
      );
      throw new InternalServerErrorException(
        'Failed to delete calculation.' +
          (deleteError.details ? ` (${deleteError.details})` : ''),
      );
    }

    this.logger.log(
      `Calculation ${id} soft deleted successfully by user ${user.id}`,
    );
  }

  /**
   * Update calculation taxes
   */
  private async updateCalculationTaxes(
    calculationId: string,
    taxes: any[] | null,
  ): Promise<void> {
    const supabase = this.supabaseService.getClient();

    // First, delete existing taxes for this calculation
    const { error: deleteError } = await supabase
      .from('calculation_taxes')
      .delete()
      .eq('calculation_id', calculationId);

    if (deleteError) {
      this.logger.error(
        `Failed to delete existing taxes for calculation ${calculationId}: ${deleteError.message}`,
      );
      throw new InternalServerErrorException('Failed to update taxes');
    }

    // If taxes array is provided and not empty, insert new taxes
    if (taxes && Array.isArray(taxes) && taxes.length > 0) {
      // Get current subtotal for tax calculations
      const { data: calculation, error: calcError } = await supabase
        .from('calculation_history')
        .select('subtotal')
        .eq('id', calculationId)
        .single();

      if (calcError || !calculation) {
        this.logger.error(
          `Failed to get calculation subtotal for tax calculation: ${calcError?.message}`,
        );
        throw new InternalServerErrorException('Failed to calculate taxes');
      }

      const subtotal = parseFloat(calculation.subtotal.toString()) || 0;

      // Prepare tax records for insertion
      const taxRecords = taxes.map(tax => {
        const taxRate = parseFloat(tax.rate.toString()) || 0;
        const taxAmount = (subtotal * taxRate) / 100;

        return {
          calculation_id: calculationId,
          tax_name: tax.name,
          tax_rate: taxRate,
          tax_amount: taxAmount,
          applied_to_subtotal: subtotal,
        };
      });

      // Insert new taxes
      const { error: insertError } = await supabase
        .from('calculation_taxes')
        .insert(taxRecords);

      if (insertError) {
        this.logger.error(
          `Failed to insert new taxes for calculation ${calculationId}: ${insertError.message}`,
        );
        throw new InternalServerErrorException('Failed to save taxes');
      }
    }
  }

  /**
   * Update calculation discount
   */
  private async updateCalculationDiscount(
    calculationId: string,
    discount: any | null,
  ): Promise<void> {
    const supabase = this.supabaseService.getClient();

    // Update the discount field in calculation_history
    const { error: updateError } = await supabase
      .from('calculation_history')
      .update({ discount })
      .eq('id', calculationId);

    if (updateError) {
      this.logger.error(
        `Failed to update discount for calculation ${calculationId}: ${updateError.message}`,
      );
      throw new InternalServerErrorException('Failed to update discount');
    }
  }

  /**
   * Trigger recalculation after updates
   */
  async triggerRecalculation(id: string): Promise<void> {
    const supabase = this.supabaseService.getClient();

    try {
      this.logger.log(
        `Attempting to trigger recalculation after update for calculation ID: ${id}`,
      );
      const { error: rpcError } = await supabase.rpc(
        'recalculate_calculation_totals',
        { p_calculation_id: id },
      );

      if (rpcError) {
        this.logger.error(
          `Error calling recalculate_calculation_totals RPC for calculation ${id}: ${rpcError.message}`,
          rpcError.stack,
        );
      } else {
        this.logger.log(
          `Successfully triggered recalculation after update for calculation ID: ${id}`,
        );
      }
    } catch (recalcError: unknown) {
      const errorMessage: string =
        recalcError instanceof Error
          ? recalcError.message
          : String(recalcError);
      const errorStack: string | undefined =
        recalcError instanceof Error ? recalcError.stack : undefined;
      this.logger.error(
        `Recalculation RPC call failed unexpectedly after update for calculation ID: ${id} - ${errorMessage}`,
        errorStack,
      );
    }
  }
}
