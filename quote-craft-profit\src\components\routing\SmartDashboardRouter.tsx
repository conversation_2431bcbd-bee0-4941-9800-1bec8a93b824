import React, { useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useUserPreferences } from "@/hooks/useUserPreferences";
import DashboardPage from "@/pages/dashboard/DashboardPage";
import DashboardV2Page from "@/pages/dashboard-v2/DashboardV2Page";

/**
 * Smart Dashboard Router Component
 *
 * Automatically routes users to their preferred dashboard version based on user preferences.
 * Handles routing for '/', '/dashboard', and '/dashboard-v2' paths.
 */
export const SmartDashboardRouter: React.FC = () => {
  const { preferences, isLoading } = useUserPreferences();
  const navigate = useNavigate();
  const location = useLocation();

  const preferredVersion = preferences?.dashboardVersion || "v1";

  useEffect(() => {
    // Don't redirect if still loading preferences
    if (isLoading) return;

    // Handle automatic routing for dashboard paths
    if (location.pathname === "/" || location.pathname === "/dashboard") {
      // If user prefers V2, redirect to V2
      if (preferredVersion === "v2") {
        navigate("/dashboard-v2", { replace: true });
        return;
      }
      // If user prefers V1 and is on /dashboard, redirect to root
      if (preferredVersion === "v1" && location.pathname === "/dashboard") {
        navigate("/", { replace: true });
        return;
      }
      // If user prefers V1 and is on root, stay on current page (no redirect needed)
    }
  }, [preferredVersion, isLoading, location.pathname, navigate]);

  // Show loading state while preferences are loading
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-500">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  // For root path and /dashboard path, render the preferred dashboard
  if (location.pathname === "/" || location.pathname === "/dashboard") {
    return preferredVersion === "v2" ? <DashboardV2Page /> : <DashboardPage />;
  }

  // For explicit V2 path, render V2 dashboard
  if (location.pathname === "/dashboard-v2") {
    return <DashboardV2Page />;
  }

  // Default to V1 dashboard
  return <DashboardPage />;
};

/**
 * Dashboard Version Indicator Component
 *
 * Shows which dashboard version is currently active and allows quick switching
 */
export const DashboardVersionIndicator: React.FC = () => {
  const { preferences, updateDashboardVersion } = useUserPreferences();
  const navigate = useNavigate();
  const location = useLocation();

  const currentVersion = location.pathname === "/dashboard-v2" ? "v2" : "v1";
  const preferredVersion = preferences?.dashboardVersion || "v1";

  const handleVersionSwitch = () => {
    const newVersion = currentVersion === "v1" ? "v2" : "v1";
    const newPath = newVersion === "v1" ? "/" : "/dashboard-v2";

    // Update preference
    updateDashboardVersion(newVersion);

    // Navigate to new dashboard
    navigate(newPath);
  };

  // Only show indicator on dashboard pages (including /dashboard route)
  if (!location.pathname.match(/^\/(dashboard-v2|dashboard)?$/)) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-3">
        <div className="flex items-center gap-3">
          <div className="text-sm">
            <span className="text-gray-500">Dashboard:</span>
            <span className="font-medium ml-1">
              {currentVersion.toUpperCase()}
            </span>
            {preferredVersion !== currentVersion && (
              <span className="text-xs text-orange-500 ml-1">
                (Not preferred)
              </span>
            )}
          </div>
          <button
            onClick={handleVersionSwitch}
            className="text-xs bg-blue-100 hover:bg-blue-200 dark:bg-blue-900 dark:hover:bg-blue-800 text-blue-800 dark:text-blue-200 px-2 py-1 rounded transition-colors"
          >
            Switch to {currentVersion === "v1" ? "V2" : "V1"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default SmartDashboardRouter;
