import { useState } from "react";
import { toast } from "sonner";
import { Tax, Discount } from "../../utils/calculationUtils";

interface UseTaxDiscountManagementProps {
  initialTaxes?: Tax[];
  initialDiscount?: Discount;
  onTaxesChange?: (taxes: Tax[]) => void;
  onDiscountChange?: (discount: Discount) => void;
}

/**
 * Custom hook for tax and discount management
 * Handles the state and logic for managing taxes and discounts
 */
export const useTaxDiscountManagement = ({
  initialTaxes = [],
  initialDiscount,
  onTaxesChange,
  onDiscountChange,
}: UseTaxDiscountManagementProps) => {
  const [taxes, setTaxes] = useState<Tax[]>(initialTaxes);
  const [isAddingTax, setIsAddingTax] = useState(false);
  const [newTaxName, setNewTaxName] = useState("");
  const [newTaxPercentage, setNewTaxPercentage] = useState("");

  const [discountObj, setDiscountObj] = useState<Discount>(
    initialDiscount || {
      name: "Discount",
      type: "fixed", // Always fixed price for discounts
      value: 0,
    }
  );
  const [isAddingDiscount, setIsAddingDiscount] = useState(false);
  const [discountAmount, setDiscountAmount] = useState("");

  // Handle adding a new tax - now uses Backend API
  const handleAddTax = () => {
    if (!newTaxName.trim()) {
      toast.error("Please enter a tax name");
      return;
    }

    const rate = parseFloat(newTaxPercentage);
    if (isNaN(rate) || rate < 0) {
      toast.error("Please enter a valid tax percentage (0% or higher)");
      return;
    }
    if (rate > 100) {
      toast.error("Tax percentage cannot exceed 100%");
      return;
    }

    // Create a new tax with the enhanced structure
    const newTax: Tax = {
      id: Date.now().toString(), // Temporary ID for UI
      name: newTaxName.trim(),
      rate: rate,
      type: "percentage", // Default type
      basis: "subtotal", // Default basis
    };

    // Update local state immediately for UI responsiveness
    const updatedTaxes = [...taxes, newTax];
    setTaxes(updatedTaxes);
    setNewTaxName("");
    setNewTaxPercentage("");
    setIsAddingTax(false);

    // Call the callback if provided (for backward compatibility)
    if (onTaxesChange) {
      onTaxesChange(updatedTaxes);
    }

    // Note: The actual API call will be handled by the useTaxesAndDiscounts hook
    // when the parent component calls addTax method
  };

  // Handle editing a tax - now uses Backend API
  const handleEditTax = (id: string, name: string, rate: number) => {
    // Update local state immediately for UI responsiveness
    const updatedTaxes = taxes.map((tax) =>
      tax.id === id ? { ...tax, name, rate } : tax
    );
    setTaxes(updatedTaxes);

    // Call the callback if provided (for backward compatibility)
    if (onTaxesChange) {
      onTaxesChange(updatedTaxes);
    }

    // Note: The actual API call will be handled by the useTaxesAndDiscounts hook
    // when the parent component calls editTax method
  };

  // Handle removing a tax
  const handleRemoveTax = (id: string) => {
    const updatedTaxes = taxes.filter((tax) => tax.id !== id);
    setTaxes(updatedTaxes);

    // Call the callback if provided
    if (onTaxesChange) {
      onTaxesChange(updatedTaxes);
    }
  };

  // Handle adding a discount
  const handleAddDiscount = () => {
    const value = parseFloat(discountAmount);
    if (isNaN(value) || value <= 0) {
      toast.error("Please enter a valid discount amount");
      return;
    }

    // Create a new discount with the simplified structure (always fixed price)
    const newDiscount: Discount = {
      name: "Discount",
      type: "fixed", // Always fixed price for discounts
      value: value,
    };

    setDiscountObj(newDiscount);
    setDiscountAmount("");
    setIsAddingDiscount(false);

    // Call the callback if provided
    if (onDiscountChange) {
      onDiscountChange(newDiscount);
    }
  };

  // Handle editing the discount
  const handleEditDiscount = (value: number) => {
    // Create an updated discount with the new value (always fixed price)
    const updatedDiscount: Discount = {
      name: "Discount",
      type: "fixed", // Always fixed price for discounts
      value: value,
    };

    setDiscountObj(updatedDiscount);

    // Call the callback if provided
    if (onDiscountChange) {
      onDiscountChange(updatedDiscount);
    }
  };

  // Handle removing the discount
  const handleRemoveDiscount = () => {
    // Create an empty discount with the simplified structure (always fixed price)
    const emptyDiscount: Discount = {
      name: "Discount",
      type: "fixed", // Always fixed price for discounts
      value: 0,
    };

    setDiscountObj(emptyDiscount);

    // Call the callback if provided
    if (onDiscountChange) {
      onDiscountChange(emptyDiscount);
    }
  };

  return {
    // Tax state
    taxes,
    isAddingTax,
    newTaxName,
    newTaxPercentage,
    setIsAddingTax,
    setNewTaxName,
    setNewTaxPercentage,
    handleAddTax,
    handleEditTax,
    handleRemoveTax,

    // Discount state
    discountObj,
    isAddingDiscount,
    discountAmount,
    setIsAddingDiscount,
    setDiscountAmount,
    handleAddDiscount,
    handleEditDiscount,
    handleRemoveDiscount,
  };
};
