/**
 * Tax management service for calculations
 * Uses Backend API endpoints instead of direct Supabase calls
 */

import { getAuthenticatedApiClient } from "@/integrations/api/client";

// Updated Tax interface to match backend DTO
export interface Tax {
  id?: string;
  name: string;
  rate: number; // Percentage
  amount?: number; // Calculated amount
  applied_to_subtotal?: number;
  type?: string; // 'percentage' | 'fixed'
  basis?: string; // 'subtotal' | 'total'
}

export interface CreateTaxRequest {
  tax_name: string;
  tax_rate: number;
  type?: string;
  basis?: string;
}

export interface UpdateTaxRequest {
  tax_name?: string;
  tax_rate?: number;
  type?: string;
  basis?: string;
}

/**
 * Add a tax to a calculation
 */
export const addTaxToCalculation = async (
  calculationId: string,
  tax: CreateTaxRequest
): Promise<Tax> => {
  try {
    const apiClient = await getAuthenticatedApiClient();
    const response = await apiClient.post(
      `/calculations/${calculationId}/taxes`,
      tax
    );
    return transformBackendTaxToFrontend(response.data);
  } catch (error) {
    console.error("Error adding tax:", error);
    throw error;
  }
};

/**
 * Get all taxes for a calculation
 */
export const getCalculationTaxes = async (
  calculationId: string
): Promise<Tax[]> => {
  try {
    const apiClient = await getAuthenticatedApiClient();
    const response = await apiClient.get(
      `/calculations/${calculationId}/taxes`
    );
    return response.data.map(transformBackendTaxToFrontend);
  } catch (error) {
    console.error("Error fetching taxes:", error);
    throw error;
  }
};

/**
 * Update a specific tax
 */
export const updateCalculationTax = async (
  calculationId: string,
  taxId: string,
  updates: UpdateTaxRequest
): Promise<Tax> => {
  try {
    const apiClient = await getAuthenticatedApiClient();
    const response = await apiClient.put(
      `/calculations/${calculationId}/taxes/${taxId}`,
      updates
    );
    return transformBackendTaxToFrontend(response.data);
  } catch (error) {
    console.error("Error updating tax:", error);
    throw error;
  }
};

/**
 * Remove a tax from a calculation
 */
export const removeCalculationTax = async (
  calculationId: string,
  taxId: string
): Promise<void> => {
  try {
    const apiClient = await getAuthenticatedApiClient();
    await apiClient.delete(`/calculations/${calculationId}/taxes/${taxId}`);
  } catch (error) {
    console.error("Error removing tax:", error);
    throw error;
  }
};

/**
 * Transform backend tax data to frontend Tax interface
 */
function transformBackendTaxToFrontend(backendTax: any): Tax {
  return {
    id: backendTax.id,
    name: backendTax.tax_name,
    rate: parseFloat(backendTax.tax_rate) || 0,
    amount: parseFloat(backendTax.tax_amount) || 0,
    applied_to_subtotal: parseFloat(backendTax.applied_to_subtotal) || 0,
    type: backendTax.type || 'percentage',
    basis: backendTax.basis || 'subtotal',
  };
}

/**
 * Transform frontend tax data to backend format
 */
export function transformFrontendTaxToBackend(frontendTax: Tax): CreateTaxRequest {
  return {
    tax_name: frontendTax.name,
    tax_rate: frontendTax.rate,
    type: frontendTax.type || 'percentage',
    basis: frontendTax.basis || 'subtotal',
  };
}
