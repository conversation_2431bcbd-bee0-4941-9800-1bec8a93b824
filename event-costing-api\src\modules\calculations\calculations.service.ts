import {
  Injectable,
  Logger,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { SupabaseService } from '../../core/supabase/supabase.service';
import { User } from '@supabase/supabase-js';
import { CreateCalculationDto } from './dto/create-calculation.dto';
import { ListCalculationsDto } from './dto/list-calculations.dto';
import { CalculationDetailDto } from './dto/calculation-detail.dto';
import { CalculationSummaryDto } from './dto/paginated-calculations.dto';
import { UpdateCalculationDto } from './dto/update-calculation.dto';
import { PaginatedResponseDto } from '../../shared/dtos/paginated-response.dto';
import { UpdateCalculationStatusDto } from './dto/update-calculation-status.dto';

// Import the new specialized services
import { CalculationCrudService } from './services/calculation-crud.service';
import { CalculationVenueService } from './services/calculation-venue.service';
import { CalculationValidationService } from './services/calculation-validation.service';
import { CalculationTransformationService } from './services/calculation-transformation.service';
import { CalculationStatusService } from './services/calculation-status.service';
import { CalculationLogicService } from './calculation-logic.service';
import { CalculationTemplateService } from './calculation-template.service';

@Injectable()
export class CalculationsService {
  private readonly logger = new Logger(CalculationsService.name);

  constructor(
    private readonly supabaseService: SupabaseService,
    private readonly calculationLogicService: CalculationLogicService,
    private readonly calculationTemplateService: CalculationTemplateService,
    private readonly calculationCrudService: CalculationCrudService,
    private readonly calculationVenueService: CalculationVenueService,
    private readonly calculationValidationService: CalculationValidationService,
    private readonly calculationTransformationService: CalculationTransformationService,
    private readonly calculationStatusService: CalculationStatusService,
  ) {}

  async createCalculation(
    createCalculationDto: CreateCalculationDto,
    user: User,
  ): Promise<string> {
    // Delegate to CRUD service for calculation creation
    const calculationId = await this.calculationCrudService.createCalculation(
      createCalculationDto,
      user,
    );

    // Handle venue associations if provided
    if (
      createCalculationDto.venue_ids &&
      createCalculationDto.venue_ids.length > 0
    ) {
      await this.calculationVenueService.addVenuesToCalculation(
        calculationId,
        createCalculationDto.venue_ids,
      );
    }

    return calculationId;
  }

  async findUserCalculations(
    user: User,
    queryParams: ListCalculationsDto,
  ): Promise<PaginatedResponseDto<CalculationSummaryDto>> {
    // Delegate to CRUD service
    return this.calculationCrudService.findUserCalculations(user, queryParams);
  }

  async findCalculationById(
    id: string,
    user: User,
  ): Promise<CalculationDetailDto> {
    // Get raw calculation data from CRUD service
    const raw = await this.calculationCrudService.findCalculationRawById(
      id,
      user,
    );

    // Fetch venues using venue service
    const venues =
      await this.calculationVenueService.fetchCalculationVenues(id);

    // Transform data using transformation service
    return this.calculationTransformationService.mapRawToDetailDto(raw, venues);
  }

  async updateCalculation(
    id: string,
    updateDto: UpdateCalculationDto,
    user: User,
  ): Promise<CalculationDetailDto> {
    this.logger.log(`User ${user.id} updating calculation ${id}`);

    // Validate ownership using validation service
    await this.calculationValidationService.checkCalculationOwnership(
      id,
      user.id,
    );

    // Extract venue_ids from the updateDto to handle separately
    const { venue_ids, ...calculationUpdateData } = updateDto;

    // Update calculation data using CRUD service
    await this.calculationCrudService.updateCalculationData(
      id,
      calculationUpdateData,
      user,
    );

    // Handle venue updates if provided using venue service
    if (venue_ids !== undefined) {
      await this.calculationVenueService.updateCalculationVenues(
        id,
        venue_ids || [],
      );
    }

    // Trigger recalculation using CRUD service
    await this.calculationCrudService.triggerRecalculation(id);

    this.logger.log(`Calculation ${id} updated, re-fetching details.`);
    return this.findCalculationById(id, user);
  }

  async deleteCalculation(id: string, user: User): Promise<void> {
    // Delegate to CRUD service
    await this.calculationCrudService.deleteCalculation(id, user);
  }

  public async checkCalculationOwnership(
    calculationId: string,
    userId: string,
  ): Promise<void> {
    // Delegate to validation service
    await this.calculationValidationService.checkCalculationOwnership(
      calculationId,
      userId,
    );
  }

  /**
   * Find calculation by ID for export processing (bypasses User object requirement)
   * This method is specifically for background export jobs
   */
  public async findCalculationForExport(
    calculationId: string,
    userId: string,
  ): Promise<CalculationDetailDto> {
    this.logger.log(
      `[EXPORT] Starting findCalculationForExport for calc ${calculationId}, user ${userId}`,
    );

    try {
      // First check ownership
      this.logger.log(
        `[EXPORT] Checking ownership for calc ${calculationId}, user ${userId}`,
      );
      await this.checkCalculationOwnership(calculationId, userId);
      this.logger.log(
        `[EXPORT] Ownership check passed for calc ${calculationId}`,
      );

      // Create a minimal User object for the CRUD service
      const systemUser: User = {
        id: userId,
        email: '<EMAIL>',
        app_metadata: {},
        user_metadata: {},
        aud: '',
        created_at: '',
      };

      // Get raw calculation data from CRUD service
      this.logger.log(
        `[EXPORT] Fetching raw calculation data for calc ${calculationId}`,
      );
      const raw = await this.calculationCrudService.findCalculationRawById(
        calculationId,
        systemUser,
      );
      this.logger.log(
        `[EXPORT] Raw calculation data fetched successfully for calc ${calculationId}`,
      );

      // Fetch venues using venue service
      this.logger.log(`[EXPORT] Fetching venues for calc ${calculationId}`);
      const venues =
        await this.calculationVenueService.fetchCalculationVenues(
          calculationId,
        );
      this.logger.log(
        `[EXPORT] Venues fetched successfully for calc ${calculationId}, count: ${venues?.length || 0}`,
      );

      // Transform data using transformation service
      this.logger.log(`[EXPORT] Transforming data for calc ${calculationId}`);
      const result = this.calculationTransformationService.mapRawToDetailDto(
        raw,
        venues,
      );
      this.logger.log(
        `[EXPORT] Data transformation completed successfully for calc ${calculationId}`,
      );

      return result;
    } catch (error) {
      this.logger.error(
        `[EXPORT] Error in findCalculationForExport for calc ${calculationId}, user ${userId}: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined,
      );
      throw error;
    }
  }

  // Note: _fetchCalculationVenues and _mapRawToDetailDto methods have been moved to specialized services

  async updateStatus(
    id: string,
    updateStatusDto: UpdateCalculationStatusDto,
    userId: string,
  ): Promise<void> {
    // Delegate to status service
    await this.calculationStatusService.updateStatus(
      id,
      updateStatusDto,
      userId,
    );
  }

  /**
   * Get calculation summary for template creation dialog
   */
  async getCalculationSummary(id: string, user: User): Promise<any> {
    this.logger.log(`Fetching calculation summary for ID: ${id}`);

    // Check ownership first
    await this.checkCalculationOwnership(id, user.id);

    const supabase = this.supabaseService.getClient();

    // Fetch calculation with related data
    const { data: calculation, error: calcError } = await supabase
      .from('calculation_history')
      .select(
        `
        id, name, total, attendees, event_type_id,
        currency:currencies(id, code),
        city:cities(id, name),
        event_type:event_types(id, name),
        venues:calculation_venues(
          venue:venues(id, name)
        )
      `,
      )
      .eq('id', id)
      .eq('is_deleted', false)
      .single();

    if (calcError) {
      this.logger.error(
        `Error fetching calculation ${id}: ${calcError.message}`,
      );
      throw new InternalServerErrorException('Could not retrieve calculation.');
    }

    if (!calculation) {
      throw new NotFoundException(`Calculation with ID ${id} not found.`);
    }

    // Count standard packages (line items with package_id)
    const { count: standardPackagesCount, error: packagesError } =
      await supabase
        .from('calculation_line_items')
        .select('*', { count: 'exact', head: true })
        .eq('calculation_id', id)
        .not('package_id', 'is', null);

    if (packagesError) {
      this.logger.error(
        `Error counting standard packages: ${packagesError.message}`,
      );
      throw new InternalServerErrorException(
        'Could not count standard packages.',
      );
    }

    // Count custom items
    const { count: customItemsCount, error: customError } = await supabase
      .from('calculation_custom_items')
      .select('*', { count: 'exact', head: true })
      .eq('calculation_id', id);

    if (customError) {
      this.logger.error(`Error counting custom items: ${customError.message}`);
      throw new InternalServerErrorException('Could not count custom items.');
    }

    // Use transformation service to transform calculation summary
    const transformedCalculation =
      this.calculationTransformationService.mapCalculationSummary(calculation);

    const summary = {
      ...transformedCalculation,
      standardPackagesCount: standardPackagesCount || 0,
      customItemsCount: customItemsCount || 0,
    };

    this.logger.log(
      `Calculation summary for ${id}: ${JSON.stringify({
        ...summary,
        venues: `[${summary.venues?.length || 0} venues]`,
      })}`,
    );

    return summary;
  }
}
