import { SupabaseService } from '../../core/supabase/supabase.service';
import { User } from '@supabase/supabase-js';
import { CreateCalculationDto } from './dto/create-calculation.dto';
import { ListCalculationsDto } from './dto/list-calculations.dto';
import { CalculationDetailDto } from './dto/calculation-detail.dto';
import { CalculationSummaryDto } from './dto/paginated-calculations.dto';
import { UpdateCalculationDto } from './dto/update-calculation.dto';
import { PaginatedResponseDto } from '../../shared/dtos/paginated-response.dto';
import { UpdateCalculationStatusDto } from './dto/update-calculation-status.dto';
import { CreateTaxDto, UpdateTaxDto } from './dto/tax-detail-item.dto';
import { CalculationCrudService } from './services/calculation-crud.service';
import { CalculationVenueService } from './services/calculation-venue.service';
import { CalculationValidationService } from './services/calculation-validation.service';
import { CalculationTransformationService } from './services/calculation-transformation.service';
import { CalculationStatusService } from './services/calculation-status.service';
import { CalculationLogicService } from './calculation-logic.service';
import { CalculationTemplateService } from './calculation-template.service';
export declare class CalculationsService {
    private readonly supabaseService;
    private readonly calculationLogicService;
    private readonly calculationTemplateService;
    private readonly calculationCrudService;
    private readonly calculationVenueService;
    private readonly calculationValidationService;
    private readonly calculationTransformationService;
    private readonly calculationStatusService;
    private readonly logger;
    constructor(supabaseService: SupabaseService, calculationLogicService: CalculationLogicService, calculationTemplateService: CalculationTemplateService, calculationCrudService: CalculationCrudService, calculationVenueService: CalculationVenueService, calculationValidationService: CalculationValidationService, calculationTransformationService: CalculationTransformationService, calculationStatusService: CalculationStatusService);
    createCalculation(createCalculationDto: CreateCalculationDto, user: User): Promise<string>;
    findUserCalculations(user: User, queryParams: ListCalculationsDto): Promise<PaginatedResponseDto<CalculationSummaryDto>>;
    findCalculationById(id: string, user: User): Promise<CalculationDetailDto>;
    updateCalculation(id: string, updateDto: UpdateCalculationDto, user: User): Promise<CalculationDetailDto>;
    deleteCalculation(id: string, user: User): Promise<void>;
    checkCalculationOwnership(calculationId: string, userId: string): Promise<void>;
    findCalculationForExport(calculationId: string, userId: string): Promise<CalculationDetailDto>;
    updateStatus(id: string, updateStatusDto: UpdateCalculationStatusDto, userId: string): Promise<void>;
    getCalculationSummary(id: string, user: User): Promise<any>;
    addTax(calculationId: string, createTaxDto: CreateTaxDto): Promise<any>;
    getTaxes(calculationId: string): Promise<any[]>;
    updateTax(calculationId: string, taxId: string, updateTaxDto: UpdateTaxDto): Promise<any>;
    removeTax(calculationId: string, taxId: string): Promise<void>;
    private recalculateCalculationTotals;
}
